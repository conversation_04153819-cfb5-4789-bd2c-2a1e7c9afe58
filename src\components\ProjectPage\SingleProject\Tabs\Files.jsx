"use client";
import { deleteFolder, getFolders } from "@/app/action";
import Empty from "@/components/Common/Empty";
import InfiniteScroll from "@/components/Common/InfiniteScroll";
import useApiRequest from "@/components/helper/hook/useApiRequest";
import ProjectSkeleton from "@/components/Loader/ProjectSkeleton";
import FolderCard from "./FilesComponent/FolderCard";
import { useEffect } from "react";
import { useState } from "react";
import PopUpModal from "@/components/Common/PopUpModal";
import FolderModal from "./FilesComponent/FolderModal";
import { useRouter } from "next/navigation";
import { useProject } from "@/components/context/ProjectContext";
import { AddFileIcon } from "@/utils/icons";
import authStorage from "@/utils/API/AuthStorage";

const Files = ({ id }) => {
  const [dataList, setDataList] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  });
  const [isFileOpen, setIsFileOpen] = useState(false);
  const [editData, setEditData] = useState(null);
  const [deleteData, setDeleteData] = useState(null);
  const [refresh, setRefresh] = useState(false);
  const [userData, setUserData] = useState(null);
  const api = useApiRequest();
  const deleteApi = useApiRequest(false);
  const router = useRouter();

  // Reset Data
  const resetDataList = () => {
    setDataList([]);
    setPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
  };

  const { project } = useProject();

  const fetchData = (projectId) => {
    let query = {
      page: pagination.page,
      limit: pagination.limit,
      ProjectId: +projectId,
    };

    const user = authStorage.getProfileDetails();
    setUserData(user);
    api.sendRequest(
      getFolders,
      (res) => {
        setPagination((prev) => ({
          ...prev,
          total: res?.data?.totalRecords,
        }));
        if (res?.data?.data?.length && pagination?.page > 1) {
          setDataList((prev) => [...prev, ...res?.data?.data]);
        } else if (res?.data?.data?.length && pagination.page === 1) {
          setDataList([...res?.data?.data]);
        } else {
        }
      },
      query
    );
  };
  useEffect(() => {
    if (project?.id) {
      fetchData(project?.id);
    }
  }, [pagination.page, project, refresh]);
  return (
    <>
      {/* Add Folder */}
      {(project?.UserId === userData?.id ||
        project?.ProjectMembers?.[0]?.access === "write") && (
        <button
          type="button"
          onClick={() => {
            setIsFileOpen(true);
          }}
          className="tw-fixed tw-bottom-16 tw-right-6 lg:tw-bottom-auto lg:tw-absolute lg:-tw-top-[6rem] lg:tw-right-[0rem] tw-z-50 tw-bg-primary-purple tw-border-primary-purple  tw-border tw-outline-none tw-rounded-[3.125rem] tw-text-white !tw-px-4 !tw-py-4 tw-transition tw-ease-in-out tw-duration-300 hover:tw-scale-110 !tw-flex !tw-items-center !tw-gap-1.5 !tw-font-semibold"
        >
          <AddFileIcon size={20} />
          <span className="tw-hidden lg:tw-inline">Create</span>
        </button>
      )}
      <FolderModal
        isOpen={isFileOpen}
        setIsOpen={setIsFileOpen}
        editData={editData}
        setEditData={setEditData}
        setRefresh={setRefresh}
        reFetchData={resetDataList}
        defaultData={project ?? {}}
      />
      {/* Delete Modal */}
      <PopUpModal
        isLoading={deleteApi.isLoading}
        isOpen={deleteData}
        setIsOpen={setDeleteData}
        mainMessage="Delete Folder"
        subMessage="Are you sure you want to Delete Folder Permanently?"
        onConfirm={() => {
          deleteApi.sendRequest(
            deleteFolder,
            () => {
              resetDataList();
              setRefresh((prev) => !prev);
              setDeleteData(null);
            },
            deleteData?.id,

            "Folder Deleted Successfully"
          );
        }}
      />
      {dataList?.length === 0 && !api.isLoading && (
        <div className="tw-flex tw-h-[20rem] tw-justify-center tw-items-center ">
          <Empty iconRequired={false} label="No Files available!" />
        </div>
      )}

      <div className="tw-my-5 tw-grid tw-grid-cols-2 lg:tw-grid-cols-3 2xl:tw-grid-cols-4 tw-gap-7 tw-overflow-y-auto  tw-w-full ">
        {dataList?.length > 0 && !api.isLoading
          ? dataList?.map((ele) => (
              <div
                className="tw-cursor-pointer"
                onClick={() => {
                  router.push(`/files/${ele?.id}`);
                }}
                key={ele?.id}
              >
                <FolderCard
                  ele={ele}
                  editHandler={(ele) => {
                    setEditData(ele);
                  }}
                  deleteHandler={(ele) => {
                    setDeleteData(ele);
                  }}
                  showAllSettings={
                    project?.UserId === userData?.id ||
                    (ele?.UserId === userData?.id &&
                      project?.ProjectMembers?.[0]?.access === "write")
                  }
                  isOnlyEditSetting={
                    // ele?.UserId === userData?.id &&
                    project?.ProjectMembers?.[0]?.access === "write"
                  }
                />
              </div>
            ))
          : ""}
        <InfiniteScroll
          threshold={90}
          loadMoreFunction={() => {
            // if (pagination.total > dataList?.length) {
            if (
              pagination.page < Math.ceil(pagination.total / pagination.limit)
            ) {
              setPagination((prev) => ({
                ...prev,
                page: prev?.page + 1,
              }));
            }
          }}
          isLoading={api.isLoading}
          loadingComponent={<ProjectSkeleton count={6} />}
          timeout={10}
          // loadOff={loadOff}
        />
      </div>
    </>
  );
};

export default Files;
