"use client";
import {
  AppleIcon,
  GoogleIcon,
  HideEyeIcon,
  LinkedInIcon,
  SeeEyeIcon,
} from "@/utils/icons";
import { useContext, useEffect, useState } from "react";
import CustomTitle from "../Common/CustomTitle";
import CustomButton from "../Common/Custom-Button";
import GlobalForm from "../Common/Custom-Form";
import * as Yup from "yup";
import { signUpAPI } from "@/app/action";
import useApiRequest from "../helper/hook/useApiRequest";
import authStorage from "@/utils/API/AuthStorage";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import SocialLogin from "./SocialLogin";
import Link from "next/link";
import { valContext } from "../context/ValContext";
import FullScreenLoader from "../Loader/FullScreenLoader";
import { safeToast } from "@/utils/safeToast";

import Image from "next/image";
import logo from "../../../public/images/logo/logo-primary.svg";
import WebAppLogo from "../Common/WebAppLogo";

const SignupForm = ({ setActiveForm }) => {
  const api = useApiRequest(false);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const { forgotEmail, setForgotEmail } = useContext(valContext);
  const onLoginSubmit = async (e, actions) => {
    const payload = e;
    setIsLoading(true);
    api.sendRequest(
      signUpAPI,
      (res) => {
        // console.log(res)
        authStorage.setAuthDetails(res?.token);
        // authStorage.setProfileDetails(res?.user);
        // localStorage.removeItem("signup");
        localStorage?.setItem("type", "signup");
        localStorage?.setItem("forgotemail", e?.email);
        setForgotEmail(e?.email);
        localStorage?.setItem("password", e?.password);
        safeToast.success("OTP has been sent. Check your inbox.");
        // formik.resetForm();
        actions?.setSubmitting(false);

        router.push("/send-otp?q=create-account");
      },
      payload,
      "",
      (err) => {
        safeToast.error(err?.message);
        setIsLoading(false);
      }
    );
  };
  return (
    <>
      {isLoading && <FullScreenLoader />}

      <WebAppLogo className="lg:tw-hidden" />
      <CustomTitle
        name="Create an account"
        className=" tw-text-primary-black tw-font-bold tw-text-center  sm:tw-text-left tw-my-4 lg:tw-m-0"
        textClassName="lg:!tw-text-incenti-24 !tw-text-3xl"
      />
      <GlobalForm
        // loading={api.isLoading}
        fields={[
          { name: "email", label: "Email", type: "email", required: true },
          {
            name: "password",
            label: "Create Password",
            type: "passwordWithVal",
            required: true,
          },
        ]}
        initialValues={{ email: forgotEmail, password: "" }}
        validationSchema={{
          email: Yup.string()
            .trim()
            .email("Invalid email")
            .required("Email is required"),
          password: Yup.string()
            .trim()
            .required("Password is required")
            .min(8, "Password must be at least 8 characters long")
            .matches(
              /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&._-])[^\s]*$/,
              "(A-Z, a-z, 0-9, @$!%*?&._-), at least 1 of each & no spaces."
            ),
        }}
        onSubmit={(e, actions) => onLoginSubmit(e, actions)}
        submitButtonText="Verify Your Email"
      />
      <div className="tw-flex tw-items-center tw-w-[22rem] lg:tw-w-[28rem] tw-gap-4 my-2">
        <div className="tw-flex-1 tw-h-px tw-bg-gray-300" />
        <span className="tw-text-sm tw-text-gray-500">or continue with</span>
        <div className="tw-flex-1 tw-h-px tw-bg-gray-300" />
      </div>

      {/* Social Login Buttons */}
      <div className="tw-w-full">
        <SocialLogin isRegister={false} />
      </div>

      {/* Sign Up Link */}
      <p className="tw-text-sm tw-text-primary-black">
        Already have an account?
        <Link
          onClick={() => {
            setForgotEmail("");
            authStorage.deleteAuthDetails();
          }}
          href={"/login"}
          className="tw-text-primary-purple tw-font-bold hover:tw-underline tw-cursor-pointer"
        >
          &nbsp;Sign In
        </Link>
      </p>
    </>
  );
};

export default SignupForm;
