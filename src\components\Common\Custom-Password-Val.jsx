import { <PERSON>, <PERSON>, EyeOff } from "lucide-react";
import React, { useState } from "react";
import useApiRequest from "../helper/hook/useApiRequest";
import { useRouter } from "next/navigation";
// import { Field } from 'formik';

const CustomPasswordVal = ({
  name,
  placeholder,
  label,
  Field,
  setFieldValue,
  formik,
  required,
}) => {
  const api = useApiRequest();
  const router = useRouter();
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const isStrongPassword = (password) => {
    return (
      /[A-Z]/.test(password) && // At least 1 uppercase
      /[0-9]/.test(password) && // At least 1 number
      /[!@#$%^&*(),.?":{}|<>]/.test(password) && // At least 1 Special character
      password.length >= 8 // Minimum 8 characters
    );
  };

  // Handle password change
  const handlePasswordChange = (e) => {
    setPassword(e.target.value);
    setError(""); // Reset error on change
  };
  return (
    <>
      <div className="tw-relative tw-w-[22rem] lg:tw-w-[28rem]">
        <Field
          type={showPassword ? "text" : "password"}
          name={name}
          id={name}
          // placeholder={placeholder || label}
          className="tw-peer tw-bg-[#F1F2F3] tw-font-semibold tw-rounded-[14px] tw-pt-6 tw-pb-4 tw-ps-5 tw-pe-10 tw-outline-none tw-w-[100%] tw-text-primary-black"
          onChange={(e) => {
            handlePasswordChange(e); // Call your function
            setFieldValue(name, e.target.value); // Update Formik state
          }}
        />

        <button
          type="button"
          className="tw-absolute tw-right-3 tw-top-6 tw-text-gray-600"
          onClick={() => setShowPassword(!showPassword)}
        >
          {!showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
        </button>
        <label
          htmlFor={name}
          className={`tw-absolute tw-left-5 tw-top-5 tw-text-lg tw-text-primary-black tw-transition-all tw-duration-300 tw-pointer-events-none 
                  peer-placeholder-shown:tw-top-6 peer-placeholder-shown:tw-text-lg 
                  ${
                    formik.values[name]
                      ? "!tw-top-2 tw-text-xs"
                      : "peer-focus:tw-top-2 peer-focus:tw-text-xs"
                  }   `}
        >
          {label}
          {required && "*"}
        </label>
      </div>

      {/* Password Strength */}
      <div className="tw-flex tw-grid-cols-2 tw-gap-2 tw-text-xs lg:tw-text-sm tw-my-3">
        <div
          className={`tw-flex tw-items-center  tw-rounded-md tw-px-2.5 tw-py-1 ${
            /[A-Z]/.test(password)
              ? "tw-bg-green-100 tw-text-green-600"
              : "tw-bg-[#F1F2F3]  tw-text-[#787E89]"
          }`}
        >
          {/[A-Z]/.test(password) && <Check className="tw-mr-1" size={16} />} 1
          uppercase
        </div>
        <div
          className={`tw-flex tw-items-center  tw-rounded-md tw-px-2.5 tw-py-1 ${
            password.length >= 8
              ? "tw-bg-green-100 tw-text-green-600"
              : "tw-bg-[#F1F2F3]  tw-text-[#787E89]"
          }`}
        >
          {password.length >= 8 && <Check className="tw-mr-1" size={16} />} 8
          characters
        </div>
        <div
          className={`tw-flex tw-items-center  tw-rounded-md tw-px-2.5 tw-py-1 ${
            /[0-9]/.test(password)
              ? "tw-bg-green-100 tw-text-green-600"
              : "tw-bg-[#F1F2F3]  tw-text-[#787E89]"
          }`}
        >
          {/[0-9]/.test(password) && <Check className="tw-mr-1" size={16} />} 1
          number
        </div>
      </div>
      <div
        className={`tw-flex tw-w-[45%] tw-items-center tw-text-xs lg:tw-text-sm tw-px-2.5 tw-py-1 tw-rounded-md ${
          /[!@#$%^&*(),.?":{}|<>]/.test(password)
            ? "tw-bg-green-100 tw-text-green-600"
            : "tw-bg-[#F1F2F3]  tw-text-[#787E89]"
        }`}
      >
        {/[!@#$%^&*(),.?":{}|<>]/.test(password) && (
          <Check className="tw-mr-1" size={16} />
        )}{" "}
        1 Special character
      </div>
    </>
  );
};

export default CustomPasswordVal;
