"use client";
import authStorage from "@/utils/API/AuthStorage";
import { getToken, isFunctionEmpty } from "@/utils/function";
import { safeToast, setAllowToasts } from "@/utils/safeToast";

import { useRouter } from "next/navigation";
import { useCallback, useState } from "react";
import toast from "react-hot-toast";

const useApiRequest = (defaultLoading = true) => {
  const [isLoading, setIsLoading] = useState(defaultLoading);

  const router = useRouter();

  // console.log(val)
  const sendRequest = useCallback(
    async (
      functionCall,
      responseHandler,
      payload,
      successMessage = "",
      // successMessage = () => { },
      errorHandler = null
      // errorHandler = () => { }
    ) => {
      setIsLoading(true);

      try {
        let data;
        data = await functionCall(payload);


        if (data?.status === 200 || data?.status === "success") {
          if (responseHandler) {
            responseHandler(data);
            if (successMessage) safeToast.success(successMessage);
            // successMessage("Success");
          }
        } else {
          throw data;
        }
      } catch (error) {
        console.log(error)
        setIsLoading(false);


        if (isFunctionEmpty(errorHandler)) {
          // errorHandler(error?.message);
          errorHandler(error);
        }
        if ((error?.status === 401 && error?.message === "Access not allowed") || (error?.response?.status === 401 && error?.message === "Access not allowed")) {
          safeToast.error(error?.message)
          authStorage.deleteAuthDetails();
          progress.start()
          // setAllowToasts(false)
          router.push("/login")
          return
        }

      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  return { isLoading, sendRequest };
};

export default useApiRequest;