import { Check, Eye, EyeOff } from "lucide-react";
import React, { useState } from "react";
import CustomTitle from "../Common/CustomTitle";
import CustomButton from "../Common/Custom-Button";
import { useRouter } from "next/navigation";
import useApiRequest from "../helper/hook/useApiRequest";
import authStorage from "@/utils/API/AuthStorage";
import toast from "react-hot-toast";
import * as Yup from "yup";
import { resetPasswordAPI } from "@/app/action";
import FullScreenLoader from "../Loader/FullScreenLoader";

import { safeToast } from "@/utils/safeToast";
import WebAppLogo from "../Common/WebAppLogo";
import GlobalForm from "../Common/Custom-Form";

const ResetPassForm = ({ setActiveForm }) => {
  const api = useApiRequest(false);
  const router = useRouter();

  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const onLoginSubmit = (values, actions) => {
    // console.log(values);
    setIsLoading(true);
    api.sendRequest(
      resetPasswordAPI,
      (res) => {
        authStorage?.deleteAuthDetails();
        // safeToast.success(res?.message ?? "Password reset successful!");
        actions?.setSubmitting(false);

        router?.push("/login");
      },
      { newPassword: values?.password },
      "Password reset successful!",
      (err) => {
        // console.log(error);
        router.push("/forgot-password ");
        safeToast.error(err?.message);
        setIsLoading(false);
      }
    );
  };

  return (
    <>
      {isLoading && <FullScreenLoader />}
      <WebAppLogo className="lg:tw-hidden" />
      <CustomTitle
        name="Reset Password"
        className=" tw-text-primary-black tw-font-bold tw-text-center  sm:tw-text-left tw-my-4 lg:tw-m-0"
        textClassName="lg:!tw-text-incenti-24 !tw-text-3xl"
      />
      <GlobalForm
        loading={isLoading}
        fields={[
          {
            name: "password",
            label: "New Password",
            type: "passwordWithVal",
            required: true,
          },
          {
            name: "NewPassword",
            label: "Confirm New Password",
            type: "password",
            required: true,
          },
        ]}
        initialValues={{ password: "", NewPassword: "" }}
        validationSchema={{
          password: Yup.string()
            .trim()
            .required("Password is required")
            .min(8, "Password must be at least 8 characters long")
            .matches(
              /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&._-])[^\s]*$/,
              "(A-Z, a-z, 0-9, @$!%*?&._-), at least 1 of each & no spaces."
            ),
          NewPassword: Yup.string()
            .trim()
            .required("Both fields are required.")
            .oneOf([Yup.ref("password"), null], "Passwords do not match."),
        }}
        onSubmit={(e, actions) => onLoginSubmit(e, actions)}
        submitButtonText="Reset Password"
      />
      <button
        onClick={() => {
          router.push("/login");
          authStorage.deleteAuthDetails();
        }}
        type="button"
        className="tw-w-full tw-text-center tw-font-semibold tw-text-gray-700 hover:tw-underline"
      >
        Cancel
      </button>
    </>
  );
};

export default ResetPassForm;
