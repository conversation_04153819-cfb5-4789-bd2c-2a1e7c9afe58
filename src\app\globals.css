/* @import "quill/dist/quill.snow.css"; */

@tailwind base;
@tailwind components;
@tailwind utilities;

.ql-editor a {
  @apply !tw-text-primary-purple tw-underline;
}

html,
body {
  font-size: 14px;
}

strong {
  background: transparent !important;
}

/* styles/globals.css */
.custom-dashed-border {
  border: 2px dashed #a855f7; /* purple-500 */
  border-radius: 9999px;
  background-color: #f3e8ff; /* purple-100 */
  /* Custom dash spacing using SVG effect */
  border-style: dashed;
  border-width: 2px;
  border-image: repeating-linear-gradient(
      to right,
      #a855f7 0,
      #a855f7 8px,
      transparent 8px,
      transparent 16px
    )
    1;
}

button {
  outline: none;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  /* font-family: "var(--font-inter)"; */
}

.ql-editor {
  /* your custom styles here */
  font-size: 19px !important;
  /* etc. */
}

.project-description .ql-size-small {
  font-size: 0.9em;
  line-height: 130%;
}
.project-description .ql-size-large {
  font-size: 2.5em;
  line-height: 130%;
}
.project-description .ql-size-huge {
  font-size: 2.5em;
  line-height: 130%;
}
h1 {
  font-size: 2em;
  line-height: 130%;
  font-weight: bold;
}

h2 {
  font-size: 1.5em;
  line-height: 130%;
  font-weight: bold;
}

h3 {
  font-size: 1.17em;
  line-height: 130%;
  font-weight: bold;
}

h4 {
  font-size: 1em;
  line-height: 130%;
  font-weight: bold;
}

h5 {
  font-size: 0.83em;
  line-height: 130%;
  font-weight: bold;
}

h6 {
  font-size: 0.67em;
  line-height: 130%;
  font-weight: bold;
}

.auth-left-side {
  background-image: url("../../public/images/auth/loginCover1.png");
  background-repeat: no-repeat;
  background-size: contain;
}

.image-shadow {
  background: linear-gradient(
    180deg,
    rgba(18, 23, 34, 0.6) 33.54%,
    rgba(18, 23, 34, 0.996045) 73.96%,
    #121722 88.91%
  );
}

@keyframes textScroll {
  0% {
    transform: translateY(0%);
  }
  16.67% {
    transform: translateY(-100%);
  }
  33.33% {
    transform: translateY(-200%);
  }
  50% {
    transform: translateY(-300%);
  } /* Moves up completely */
  66.67% {
    transform: translateY(-200%);
  } /* Starts reversing */
  83.33% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(0%);
  } /* Back to the start */
}

.tw-animate-textScroll {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  animation: textScroll 9s infinite ease-in-out;
}

.custom-toast-container .Toastify__progress-bar {
  background-color: #6d11d2 !important; /* Filled progress bar color */
  opacity: 1 !important;
}

.custom-toast-container .Toastify__progress-bar--bg {
  background-color: #6d11d2 !important; /* Unfilled (track) background effect */
  opacity: 0.2 !important;
}
.custom-toast-container .Toastify__progress-bar-theme--light {
  background: #6d11d2 !important;
}

/* .no-scrollbar {
  scrollbar-width: none; 
  -ms-overflow-style: none; 
}

.no-scrollbar::-webkit-scrollbar {
  display: none; 
} */

#nprogress .bar {
  background: #6d11d2 !important;
  height: 3px !important;
}

/* loader.css */

.loader {
  font-size: 3px;
  width: 1em;
  height: 1em;
  border-radius: 50%;
  position: relative;
  text-indent: -9999em;
  animation: mulShdSpin 1.3s infinite linear;
  transform: translateZ(0);
}

@keyframes mulShdSpin {
  0%,
  100% {
    box-shadow: 0 -3em 0 0.2em, 2em -2em 0 0em, 3em 0 0 -1em, 2em 2em 0 -1em,
      0 3em 0 -1em, -2em 2em 0 -1em, -3em 0 0 -1em, -2em -2em 0 0;
  }
  12.5% {
    box-shadow: 0 -3em 0 0, 2em -2em 0 0.2em, 3em 0 0 0, 2em 2em 0 -1em,
      0 3em 0 -1em, -2em 2em 0 -1em, -3em 0 0 -1em, -2em -2em 0 -1em;
  }
  25% {
    box-shadow: 0 -3em 0 -0.5em, 2em -2em 0 0, 3em 0 0 0.2em, 2em 2em 0 0,
      0 3em 0 -1em, -2em 2em 0 -1em, -3em 0 0 -1em, -2em -2em 0 -1em;
  }
  37.5% {
    box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em, 3em 0em 0 0, 2em 2em 0 0.2em,
      0 3em 0 0em, -2em 2em 0 -1em, -3em 0em 0 -1em, -2em -2em 0 -1em;
  }
  50% {
    box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em, 3em 0 0 -1em, 2em 2em 0 0em,
      0 3em 0 0.2em, -2em 2em 0 0, -3em 0em 0 -1em, -2em -2em 0 -1em;
  }
  62.5% {
    box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em, 3em 0 0 -1em, 2em 2em 0 -1em,
      0 3em 0 0, -2em 2em 0 0.2em, -3em 0 0 0, -2em -2em 0 -1em;
  }
  75% {
    box-shadow: 0em -3em 0 -1em, 2em -2em 0 -1em, 3em 0em 0 -1em, 2em 2em 0 -1em,
      0 3em 0 -1em, -2em 2em 0 0, -3em 0em 0 0.2em, -2em -2em 0 0;
  }
  87.5% {
    box-shadow: 0em -3em 0 0, 2em -2em 0 -1em, 3em 0 0 -1em, 2em 2em 0 -1em,
      0 3em 0 -1em, -2em 2em 0 0, -3em 0em 0 0, -2em -2em 0 0.2em;
  }
}

@keyframes mulShdSpin {
  0%,
  100% {
    box-shadow: 0em -2.6em 0em 0em #ffffff,
      1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2),
      2.5em 0em 0 0em rgba(255, 255, 255, 0.2),
      1.75em 1.75em 0 0em rgba(255, 255, 255, 0.2),
      0em 2.5em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em 1.8em 0 0em rgba(255, 255, 255, 0.2),
      -2.6em 0em 0 0em rgba(255, 255, 255, 0.5),
      -1.8em -1.8em 0 0em rgba(255, 255, 255, 0.7);
  }
  12.5% {
    box-shadow: 0em -2.6em 0em 0em rgba(255, 255, 255, 0.7),
      1.8em -1.8em 0 0em #ffffff, 2.5em 0em 0 0em rgba(255, 255, 255, 0.2),
      1.75em 1.75em 0 0em rgba(255, 255, 255, 0.2),
      0em 2.5em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em 1.8em 0 0em rgba(255, 255, 255, 0.2),
      -2.6em 0em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em -1.8em 0 0em rgba(255, 255, 255, 0.5);
  }
  25% {
    box-shadow: 0em -2.6em 0em 0em rgba(255, 255, 255, 0.5),
      1.8em -1.8em 0 0em rgba(255, 255, 255, 0.7), 2.5em 0em 0 0em #ffffff,
      1.75em 1.75em 0 0em rgba(255, 255, 255, 0.2),
      0em 2.5em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em 1.8em 0 0em rgba(255, 255, 255, 0.2),
      -2.6em 0em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2);
  }
  37.5% {
    box-shadow: 0em -2.6em 0em 0em rgba(255, 255, 255, 0.2),
      1.8em -1.8em 0 0em rgba(255, 255, 255, 0.5),
      2.5em 0em 0 0em rgba(255, 255, 255, 0.7), 1.75em 1.75em 0 0em #ffffff,
      0em 2.5em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em 1.8em 0 0em rgba(255, 255, 255, 0.2),
      -2.6em 0em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2);
  }
  50% {
    box-shadow: 0em -2.6em 0em 0em rgba(255, 255, 255, 0.2),
      1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2),
      2.5em 0em 0 0em rgba(255, 255, 255, 0.5),
      1.75em 1.75em 0 0em rgba(255, 255, 255, 0.7), 0em 2.5em 0 0em #ffffff,
      -1.8em 1.8em 0 0em rgba(255, 255, 255, 0.2),
      -2.6em 0em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2);
  }
  62.5% {
    box-shadow: 0em -2.6em 0em 0em rgba(255, 255, 255, 0.2),
      1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2),
      2.5em 0em 0 0em rgba(255, 255, 255, 0.2),
      1.75em 1.75em 0 0em rgba(255, 255, 255, 0.5),
      0em 2.5em 0 0em rgba(255, 255, 255, 0.7), -1.8em 1.8em 0 0em #ffffff,
      -2.6em 0em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2);
  }
  75% {
    box-shadow: 0em -2.6em 0em 0em rgba(255, 255, 255, 0.2),
      1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2),
      2.5em 0em 0 0em rgba(255, 255, 255, 0.2),
      1.75em 1.75em 0 0em rgba(255, 255, 255, 0.2),
      0em 2.5em 0 0em rgba(255, 255, 255, 0.5),
      -1.8em 1.8em 0 0em rgba(255, 255, 255, 0.7), -2.6em 0em 0 0em #ffffff,
      -1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2);
  }
  87.5% {
    box-shadow: 0em -2.6em 0em 0em rgba(255, 255, 255, 0.2),
      1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2),
      2.5em 0em 0 0em rgba(255, 255, 255, 0.2),
      1.75em 1.75em 0 0em rgba(255, 255, 255, 0.2),
      0em 2.5em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em 1.8em 0 0em rgba(255, 255, 255, 0.5),
      -2.6em 0em 0 0em rgba(255, 255, 255, 0.7), -1.8em -1.8em 0 0em #ffffff;
  }
}

/* Other Spinner */
.dot-spinner {
  /* --uib-size: 24px; */
  --uib-speed: 0.9s;
  --uib-color: #6d11d2;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: var(--uib-size);
  width: var(--uib-size);
}

.dot-spinner__dot {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 100%;
  width: 100%;
}

.dot-spinner__dot::before {
  content: "";
  height: 20%;
  width: 20%;
  border-radius: 50%;
  background-color: var(--uib-color);
  transform: scale(0);
  opacity: 0.5;
  animation: pulse0112 calc(var(--uib-speed) * 1.111) ease-in-out infinite;
  box-shadow: 0 0 1.25rem rgba(18, 31, 53, 0.3);
}

.dot-spinner__dot:nth-child(2) {
  transform: rotate(45deg);
}

.dot-spinner__dot:nth-child(2)::before {
  animation-delay: calc(var(--uib-speed) * -0.875);
}

.dot-spinner__dot:nth-child(3) {
  transform: rotate(90deg);
}

.dot-spinner__dot:nth-child(3)::before {
  animation-delay: calc(var(--uib-speed) * -0.75);
}

.dot-spinner__dot:nth-child(4) {
  transform: rotate(135deg);
}

.dot-spinner__dot:nth-child(4)::before {
  animation-delay: calc(var(--uib-speed) * -0.625);
}

.dot-spinner__dot:nth-child(5) {
  transform: rotate(180deg);
}

.dot-spinner__dot:nth-child(5)::before {
  animation-delay: calc(var(--uib-speed) * -0.5);
}

.dot-spinner__dot:nth-child(6) {
  transform: rotate(225deg);
}

.dot-spinner__dot:nth-child(6)::before {
  animation-delay: calc(var(--uib-speed) * -0.375);
}

.dot-spinner__dot:nth-child(7) {
  transform: rotate(270deg);
}

.dot-spinner__dot:nth-child(7)::before {
  animation-delay: calc(var(--uib-speed) * -0.25);
}

.dot-spinner__dot:nth-child(8) {
  transform: rotate(315deg);
}

.dot-spinner__dot:nth-child(8)::before {
  animation-delay: calc(var(--uib-speed) * -0.125);
}

@keyframes pulse0112 {
  0%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }

  50% {
    transform: scale(1);
    opacity: 1;
  }
}

@layer utilities {
  ::-webkit-scrollbar {
    width: 0rem;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply tw-border-border;
  }
  body {
    @apply tw-bg-background tw-text-foreground;
  }
}

.show-scrollbar {
  overflow-y: auto;
}

/* Scrollbar for Chrome, Safari */
.show-scrollbar::-webkit-scrollbar {
  width: 8px; /* Adjust width */
}

.show-scrollbar::-webkit-scrollbar-thumb {
  background-color: #888; /* Scrollbar color */
  border-radius: 4px;
}

.show-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #555; /* Darker on hover */
}

.show-scrollbar::-webkit-scrollbar-track {
  background-color: #f1f1f1; /* Track color */
  border-radius: 4px;
}

/* Firefox Scrollbar */
.show-scrollbar {
  scrollbar-width: thin; /* "thin" or "auto" */
  scrollbar-color: #888 #f1f1f1; /* Thumb color and track color */
}

#post-description a {
  @apply tw-text-primary-purple tw-font-medium;
}

/* #post-description ol {
  list-style: decimal !important;
  padding-left: 2.5rem !important;
  margin-bottom: 1rem;
}

#post-description ol li {
  list-style: decimal !important;
  padding-left: 0 !important;
} */

#post-description ul {
  list-style: disc !important;
  padding-left: 2.5rem !important;
  margin-bottom: 1rem;
}
#post-description ul li {
  list-style: disc !important;
  padding-left: 0 !important;
}

#post-description img,
#post-description iframe {
  display: none;
}

.project-description img {
  margin: 1.25rem auto;
  width: 100%;
  height: 100%;
  border-radius: 1.5rem;
}
.project-description iframe {
  margin: 1.25rem auto;
  aspect-ratio: 2/1;
  width: 100%;
  /* width: 50rem; */
  height: 100%;
  /* object-fit: contain; */
  border-radius: 1.5rem;
}

.add-project-description img {
  margin: 1.25rem 0;
  width: 100%;
  height: 30rem;
  aspect-ratio: 2/3;
  object-fit: contain;
}

/* .project-description br {
  opacity: 0;
} */

/* .react-responsive-modal-overlay {
  background: rgba(0, 0, 0, 0.8) !important;
} */
/* #post-preview .carousel.carousel-slider {
  width: 100% !important;
  user-select: none !important;
} */
.carousel.carousel-slider {
  width: 24rem !important;
  user-select: none !important;
}

.carousel .control-dots {
  width: 97% !important;
  bottom: -0.375rem !important;
}

.carousel .control-dots .dot {
  margin: 0 0.375rem !important;
  width: 0.475rem !important;
  height: 0.475rem !important;
}

.post-form::placeholder {
  color: #d9d9d9; /* Change to any color */
}

/* Quill CSS */
.ql-snow {
  border: none !important;
}
/* .ql-toolbar {
  border-radius: 4px 4px 0 0 !important;
}

.ql-container {
  border-radius: 0 0 4px 4px !important;
} */

.custom-ol {
  padding-left: 2.6rem !important;
}

.ql-editor li[data-list="custom-ordered"] {
  list-style: decimal !important;
  padding-left: 0;
}

.ql-editor ul {
  padding-left: 3rem;
  margin: 1rem 0;
}

.ql-editor ul li {
  list-style: disc !important;
  padding-left: 0;
}

/* Post Form */
input[type="radio"] {
  accent-color: #6d11d2; /* Change radio button color */
}
input[type="checkbox"] {
  accent-color: #6d11d2; /* Change radio button color */
}

/* Message Loader */
.message-loader {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
  /* margin: 15px auto; */
  margin-left: 1rem;
  position: relative;
  color: #2d394a;
  box-sizing: border-box;
  animation: messageLoader 1s linear infinite alternate;
}

@keyframes messageLoader {
  0% {
    box-shadow: -20px -2px, -8px 2px, 6px -2px;
  }
  33% {
    box-shadow: -20px 2px, -8px -2px, 6px 2px;
  }
  66% {
    box-shadow: -20px -2px, -8px 2px, 6px -2px;
  }
  100% {
    box-shadow: -20px 2px, -8px -2px, 6px 2px;
  }
}

/* HTML: <div class="loader"></div> */
.chat-loader {
  width: 20px;
  aspect-ratio: 2;
  --_g: no-repeat radial-gradient(circle closest-side, #2d394a 90%, #0000);
  background: var(--_g) 0% 50%, var(--_g) 50% 50%, var(--_g) 100% 50%;
  background-size: calc(100% / 3) 50%;
  animation: l3 1s infinite linear;
}
@keyframes l3 {
  20% {
    background-position: 0% 0%, 50% 50%, 100% 50%;
  }
  40% {
    background-position: 0% 100%, 50% 0%, 100% 50%;
  }
  60% {
    background-position: 0% 50%, 50% 100%, 100% 0%;
  }
  80% {
    background-position: 0% 50%, 50% 50%, 100% 100%;
  }
}

/* Media Query */

@media only screen and (max-width: 1024px) {
  .carousel.carousel-slider {
    width: 27rem !important;
    user-select: none !important;
  }

  .project-description img {
    margin: 0.75rem 0 !important;
    width: 28rem;
  }
  .add-project-description img {
    height: 16rem !important;
    width: 100% !important;
    aspect-ratio: 1 / 1 !important;
    object-fit: contain !important;
    margin: 0.75rem 0 !important;
  }
}
@media only screen and (max-width: 423px) {
  .carousel.carousel-slider {
    width: 21rem !important;
    user-select: none !important;
  }
}
@media only screen and (min-width: 767px) {
  iframe {
    height: 100%;
  }
}

@media only screen and (max-width: 359) {
  .add-project-description img {
    height: 12rem !important;
    /* width: 100% !important;
    aspect-ratio: 1 / 1 !important;
    object-fit: contain !important;
    margin: 0 !important; */
  }
}
