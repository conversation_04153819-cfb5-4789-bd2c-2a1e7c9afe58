"use client";
import {
  deleteProject,
  getProject,
  reportProject,
  updateProject,
} from "@/app/action";
import Empty from "@/components/Common/Empty";
import InfiniteScroll from "@/components/Common/InfiniteScroll";
import PopUpModal from "@/components/Common/PopUpModal";
import ReportModal from "@/components/Common/ReportModal";
import ShareModal from "@/components/Common/ShareModal";
import { useProfile } from "@/components/context/ProfileContext";
import useApiRequest from "@/components/helper/hook/useApiRequest";
import ProjectModal from "@/components/HomePage/Form/ProjectModal";
import FormModal from "@/components/HomePage/FormModal";
import ProjectSkeleton from "@/components/Loader/ProjectSkeleton";
import MainProjectCard from "@/components/ProjectPage/MainProjectCard";
import authStorage from "@/utils/API/AuthStorage";
import { privateProjectToggle } from "@/utils/function";
import { EditPencilIcon } from "@/utils/icons";
import { safeToast } from "@/utils/safeToast";

import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";

const Projects = ({}) => {
  const [dataList, setDataList] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  });
  const [refresh, setRefresh] = useState(false);
  const [editData, setEditData] = useState(null);
  const [deleteProjectData, setDeleteProjectData] = useState(null);
  const [isOpen, setIsOpen] = useState(false);
  const [hideProject, setHideProject] = useState(null);
  const [userData, setUserData] = useState(null);
  const [isShareOpen, setIsShareOpen] = useState(false);
  const [shareData, setShareData] = useState(null);
  const [isReported, setIsReported] = useState(false);
  const [reportData, setReportData] = useState(null);
  const api = useApiRequest();
  const projectAPI = useApiRequest(false);
  const singleProjectAPI = useApiRequest(false);
  const router = useRouter();
  const { profile } = useProfile();

  // console.log(userData, userId);

  const resetDataList = () => {
    setDataList([]);
    setPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
  };

  // Fetch Data
  const fetchData = (userId) => {
    let query = {
      page: pagination.page,
      limit: pagination.limit,
      UserId: +userId,
    };
    const user = authStorage.getProfileDetails();
    setUserData(user);
    api.sendRequest(
      getProject,
      (res) => {
        if (res?.data?.data?.length && pagination?.page > 1) {
          setDataList((prev) => [...prev, ...res?.data?.data]);
        } else if (res?.data?.data?.length && pagination.page === 1) {
          setDataList((prev) => [...res?.data?.data]);
        } else {
        }
      },
      query
    );
  };
  useEffect(() => {
    if (profile?.id) {
      fetchData(profile?.id);
    }
  }, [pagination.page, profile, refresh]);

  console.log(dataList);
  return (
    <>
      {/* Report Modal */}
      <ReportModal
        title="Why are you reporting this project?"
        isOpen={isReported}
        setIsOpen={setIsReported}
        isLoading={projectAPI?.isLoading}
        onConfirm={(reason) => {
          const payload = {
            reason,
            id: reportData?.id,
          };
          projectAPI.sendRequest(
            reportProject,
            (res) => {
              safeToast.success(res?.message);
              setIsReported(false);
              resetDataList();
              setRefresh((prev) => !prev);
              setReportData(null);
            },
            payload
          );
        }}
      />
      {/* Share Modal */}
      <ShareModal
        open={isShareOpen}
        onClose={() => {
          setIsShareOpen(false);
        }}
        shareUrl={`${origin}/projects/${shareData?.slug}`}
        title={`${shareData?.name} \nCheckout this project:`}
      />
      <FormModal
        setRefresh={setRefresh}
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        isInHomePage={false}
      />
      {/* Create Button */}
      {+profile?.id === +userData?.id && (
        <button
          type="button"
          onClick={() => setIsOpen((prev) => !prev)}
          className="tw-fixed lg:tw-absolute tw-z-50 tw-bottom-[8.5rem] tw-right-4 lg:tw-z-0  lg:tw-bottom-auto lg:-tw-top-[5.8rem]  lg:tw-right-[6rem] tw-bg-primary-purple tw-border-primary-purple tw-border tw-outline-none tw-rounded-[3.125rem] tw-text-white tw-p-[1.1rem] lg:!tw-px-5 !lg:tw-py-[1.1rem] !tw-flex !tw-items-center !tw-gap-1.5 !tw-font-semibold tw-group tw-transition-transform tw-duration-300 hover:tw-scale-105"
        >
          <EditPencilIcon className="tw-h-[20px] tw-w-[20px] tw-font-semibold" />
          <span className="tw-hidden lg:tw-inline-block">Create</span>
        </button>
      )}
      {/* Project Modal */}
      <ProjectModal
        setRefresh={setRefresh}
        editData={editData}
        setEditData={setEditData}
        modalTitle={"Edit Project"}
        modalSubmitButton={"Update"}
      />
      {/* Project hide/unhide Confirm Popup */}
      <PopUpModal
        isLoading={singleProjectAPI.isLoading}
        isOpen={hideProject}
        setIsOpen={setHideProject}
        icon={null}
        mainMessage={
          privateProjectToggle(hideProject?.name, !hideProject?.hide)?.title
        }
        subMessage={
          privateProjectToggle(hideProject?.name, !hideProject?.hide)?.message
        }
        onConfirm={() => {
          const payload = {
            id: hideProject?.id,
            hide: !hideProject?.hide,
          };
          singleProjectAPI.sendRequest(
            updateProject,
            (res) => {
              // console.log(res);
              safeToast.success(
                privateProjectToggle(hideProject?.name, !hideProject?.hide)
                  ?.successMessage
              );
              setRefresh((prev) => !prev);
              setHideProject(null);
            },
            payload
          );
        }}
      />
      {/* Delete Confirm Popup */}
      <PopUpModal
        isLoading={singleProjectAPI.isLoading}
        isOpen={deleteProjectData}
        setIsOpen={setDeleteProjectData}
        mainMessage="Delete Project"
        subMessage="Are you sure you want to Delete Project Permanently?"
        onConfirm={() => {
          singleProjectAPI.sendRequest(
            deleteProject,
            () => {
              setRefresh((prev) => !prev);
              resetDataList();
              setDeleteProjectData(null);
            },
            {
              id: deleteProjectData?.id,
            },
            "Project Deleted Successfully"
          );
        }}
      />
      {!api.isLoading && dataList?.length === 0 && (
        <div className="tw-flex tw-h-[25rem] tw-justify-center tw-items-center ">
          <Empty iconRequired={false} label="No Project available!" />
        </div>
      )}
      {/* tw-max-h-[95dvh] */}
      <div className="tw-mb-20 lg:tw-my-5 tw-grid tw-grid-cols-2 md:tw-grid-cols-3 2xl:tw-grid-cols-4 tw-gap-7 tw-overflow-y-auto  tw-w-full ">
        {/* {api.isLoading && <ProjectSkeleton />} */}
        {dataList?.length > 0 && !api.isLoading
          ? dataList?.map((ele) => (
              <div
                onClick={() => {
                  if (ele?.isPrivate && userData?.id !== ele?.UserId) {
                    safeToast.success("This Project is Private");
                    return;
                  }

                  router.push(`/projects/${ele?.slug}`);
                }}
                className="tw-cursor-pointer"
                key={ele?.id}
              >
                <MainProjectCard
                  ele={ele}
                  selectedCategory={"created"}
                  setIsDelete={setDeleteProjectData}
                  setEditData={setEditData}
                  reFetchData={resetDataList}
                  setRefresh={setRefresh}
                  setHideProject={setHideProject}
                  loginUserData={userData}
                  // isMenuVisible={!ele?.isPrivate}
                  isMenuVisible={
                    !ele?.isPrivate || userData?.id === ele?.UserId
                  }
                  // loginUserData={profile}
                  shareHandler={(ele) => {
                    setIsShareOpen(true);
                    setShareData(ele);
                  }}
                  reportHandler={(ele) => {
                    setIsReported(true);
                    setReportData(ele);
                  }}
                  // isMenuVisible={profile?.id === userData?.id}
                />
              </div>
            ))
          : ""}
        <InfiniteScroll
          threshold={90}
          loadMoreFunction={() => {
            if (
              pagination.page < Math.ceil(pagination.total / pagination.limit)
            ) {
              setPagination((prev) => ({
                ...prev,
                page: prev?.page + 1,
              }));
            }
          }}
          isLoading={api.isLoading}
          loadingComponent={<ProjectSkeleton />}
          timeout={10}
          // loadOff={loadOff}
        />
      </div>
    </>
  );
};

export default Projects;
