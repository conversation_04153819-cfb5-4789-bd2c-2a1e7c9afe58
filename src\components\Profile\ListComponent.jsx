import { blurDataURL, convertUTCtoLocal } from "@/utils/function";

import Image from "next/image";
import { useRouter } from "next/navigation";
import { useState } from "react";
import UserAvatar from "../Common/UserAvatar";

const ListComponent = ({
  ele,
  followAndUnfollowHandler,
  setRefresh,
  modalDetails,
  type,
  setDataList,
  setPagination,
  communityId = null,
  loginUserData = null,
  updateListHandler,
  isFollowButtonRequired,
}) => {
  const [isFollow, setIsFollow] = useState(
    isFollowButtonRequired ? Boolean(+ele[type]["isFollowed"]) : false
  );
  const router = useRouter();

  // console.log(loginUserData?.id, ele[type]?.id);

  // const updateTheList = () => {
  //   setIsFollow((prev) => !prev);

  //   if (!isFollow) {
  //     //   // User Follow the Other User
  //     followAndUnfollowHandler(ele[type]?.id, true);
  //   } else {
  //     //   // User Removing from follower
  //     followAndUnfollowHandler(ele[type]?.id, false);
  //   }

  //   setRefresh((prev) => (typeof prev === "undefined" ? true : !prev));
  // };

  return (
    <div
      tabIndex={0}
      key={`${ele[type]?.id}-member`}
      onClick={(e) => {
        e.stopPropagation();

        router.push(`/user/${ele[type]?.slug}`);
      }}
      className="tw-flex tw-cursor-pointer tw-mb-4 tw-justify-between"
    >
      <div className="tw-flex tw-items-center tw-gap-3 ">
        <UserAvatar
          imageUrl={ele[type]?.image}
          userName={ele[type]?.firstName}
        />
        <div className="tw-mt-1">
          <p className="tw-font-bold tw-text-lg tw-not-italic !tw-text-primary-black tw-line-clamp-1">
            {`${ele[type]?.firstName ?? ""} ${ele[type]?.lastName ?? ""}  ${
              ele[type]?.isAdmin
                ? "(Admin)"
                : loginUserData?.id === ele[type]?.id
                ? "(You)"
                : ""
            }`}
          </p>
          {/* Below prop is only like modal */}
          {!isFollowButtonRequired && (
            <p className="tw-text-sm  tw-max-w-[12.5rem] tw-break-words tw-not-italic !tw-text-[#787E89]">
              {convertUTCtoLocal(ele?.createdAt, "MMM DD, YYYY") ?? ""}
            </p>
          )}
        </div>
      </div>
      <div>
        {isFollowButtonRequired && loginUserData?.id !== ele[type]?.id && (
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();

              if (!isFollow) {
                //   // User Follow the Other User
                setIsFollow((prev) => !prev);
                followAndUnfollowHandler(ele[type]?.id, true);
              } else {
                //   // User Removing from follower
                // followAndUnfollowHandler(ele[type]?.id, false);
                updateListHandler(ele[type], () => {
                  setIsFollow((prev) => !prev);
                });
                return;
              }

              setRefresh((prev) =>
                typeof prev === "undefined" ? true : !prev
              );

              // updateListHandler(ele[type], updateTheList, setDataList);
              // setIsFollow((prev) => !prev);

              // if (!isFollow) {
              //   //   // User Follow the Other User
              //   followAndUnfollowHandler(ele[type]?.id, true);
              // } else {
              //   //   // User Removing from follower
              //   followAndUnfollowHandler(ele[type]?.id, false);
              // }

              // if (type === "followingUser") {
              //   if (isFollow) {
              //     //   // User Follow the Other User
              //     followAndUnfollowHandler(ele[type]?.id, true);
              //   } else {
              //     //   // User Removing from follower
              //     followAndUnfollowHandler(ele[type]?.id, false);
              //   }
              // } else {
              //   if (!isFollow) {
              //     //   // User Follow the Other User
              //     followAndUnfollowHandler(ele[type]?.id, true);
              //   } else {
              //     //   // User Removing from follower
              //     followAndUnfollowHandler(ele[type]?.id, false);
              //   }
              // }

              // setRefresh((prev) =>
              //   typeof prev === "undefined" ? true : !prev
              // );
            }}
            className={`tw-py-2 tw-px-4 tw-rounded-full ${
              // !isFollow && (type === "User" || type === "followerUser")
              !isFollow
                ? "tw-border tw-border-primary-purple tw-text-primary-purple tw-bg-transparent"
                : "tw-border tw-bg-transparent tw-border-[#D9D9D9] tw-text-[#787E89]"
            } tw-font-semibold`}
          >
            {isFollow
              ? modalDetails.buttonText[type][0]
              : modalDetails.buttonText[type][1]}
          </button>
        )}
      </div>
    </div>
  );
};

export default ListComponent;
