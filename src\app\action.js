"use server"

// import { API_BASE_URL, buildQueryString } from "@/helper/functions";
import Services from "@/utils/API/Service";
import { API_BASE_URL, buildQueryString } from "@/utils/function";
import axios from "axios";



let headers = {
    "Content-Type": "application/json",
};
const apiType = {
    get: "GET",
    post: "POST",
    patch: "PATCH",
    delete: "DELETE"
}


export const postSocialLogin = async (data, query, token) => {
    try {
        const response = await axios.post(
            `${API_BASE_URL}/users/social-auth`,
            data,
            {
                headers: headers,
            }
        );

        return response?.data;
    } catch (error) {
        return error?.response?.data;
    }
};

export const linkedinLogin = async (data, query, token) => {
    try {
        const response = await axios.post(
            `${API_BASE_URL}/users/linkedin-auth`,
            data,
            {
                headers: headers,
            }
        );

        return response?.data;
    } catch (error) {
        return error?.response?.data;
    }
};

// export const
export const sendAPIRequest = async (
    api,
    payload
) => {
    try {
        let response;
        switch (api.type) {
            case "POST":
                response = await Services.post(api.endpoint, payload);
                break;

            case "PUT":
                response = await Services.put(api.endpoint, payload);

                break;
            case "DELETE":
                response = await Services.delete(api.endpoint);
                break;

            case "PATCH":
                response = await Services.patch(api.endpoint, payload);
                break;

            default:
                const queryParams = buildQueryString(payload);
                // const queryParams = URLSearchParams(payload)?.toString();

                // console.dir(queryParams, "query", api)

                response = await Services.get(api.endpoint + queryParams);
                break;
        }

        return response?.data;
    } catch (error) {
        // console.log(error?.response)
        return error?.response?.data;
    }
}

// ----------------> API Call Functions <--------------------

// ----------------> Auth API  <--------------------

export const loginAPI = async (payload) => {
    const login = {
        endpoint: "/users/login",
        type: "POST"
    }
    try {
        const response = await sendAPIRequest(login, payload)
        return response
    } catch (error) {
        return error
    }
}
export const signUpAPI = async (payload) => {
    const singUp = {
        endpoint: "/users/signup",
        type: apiType.post
    }
    try {
        const response = await sendAPIRequest(singUp, payload)
        return response
    } catch (error) {
        return error
    }
}
export const verifySignUpOTP = async (payload) => {
    const singUp = {
        endpoint: "/users/verify-signup",
        type: apiType.post
    }
    try {
        const response = await sendAPIRequest(singUp, payload)
        return response
    } catch (error) {
        return error
    }
}
export const forgotPasswordSend = async (payload) => {
    const forgotPassword = {
        endpoint: "/users/forgot-password",
        type: apiType.post
    }

    try {
        const response = await sendAPIRequest(forgotPassword, payload)
        return response
    } catch (error) {
        return error
    }
}
export const verifyForgotPasswordOTP = async (payload) => {
    const forgotPassword = {
        endpoint: "/users/auth/forgot-password",
        type: apiType.post
    }
    try {
        const response = await sendAPIRequest(forgotPassword, payload)
        return response
    } catch (error) {
        return error
    }
}
export const resetPasswordAPI = async (payload) => {
    const resetPassword = {
        endpoint: "/users/forgotten-password",
        type: apiType.patch
    }
    try {
        const response = await sendAPIRequest(resetPassword, payload)
        return response
    } catch (error) {
        return error
    }
}
export const updatePasswordAPI = async (payload) => {
    const updatePassword = {
        endpoint: "/users/password",
        type: apiType.patch
    }
    try {
        const response = await sendAPIRequest(updatePassword, payload)
        return response
    } catch (error) {
        return error
    }
}
export const completeUserProfile = async (payload) => {
    const updateMe = {
        endpoint: "/users/complete-profile",
        type: apiType.patch
    }
    try {
        const response = await sendAPIRequest(updateMe, payload)
        return response
    } catch (error) {
        return error
    }
}

// ----------------> Image Upload API  <--------------------

export const updateImageToURL = async (payload) => {

    const apiEndpoint = `${API_BASE_URL}/general/files`

    try {
        const response = await axios.post(
            apiEndpoint, payload,
            {
                headers: {
                    ...headers,
                    "Content-Type": "multipart/form-data",
                },
                maxContentLength: Infinity,
                maxBodyLength: Infinity,
            }
        );
        return response?.data;
    } catch (error) {
        return error?.response?.data;
    }
};

// ----------------> Chat Bot API  <--------------------

export const sendMessageToBot = async (payload) => {

    const sendData = {
        endpoint: "/general/chat-boat",
        type: apiType.post
    }

    try {
        const response = await sendAPIRequest(sendData, payload)
        return response
    } catch (error) {
        return error
    }
};




// Get Me API

export const getMyProfile = async () => {

    const getMe = {
        endpoint: "/users/get-me",
        type: apiType.get
    }
    try {
        const response = await sendAPIRequest(getMe)
        return response
    } catch (error) {
        return error
    }
}


export const updateMyProfile = async (payload) => {

    const updateMe = {
        endpoint: "/users/update-me",
        type: apiType.patch
    }
    try {
        const response = await sendAPIRequest(updateMe, payload)
        return response
    } catch (error) {
        return error
    }
}
export const deleteMyAccount = async () => {

    const deleteMe = {
        endpoint: "/users/delete-me",
        type: apiType.delete
    }
    try {
        const response = await sendAPIRequest(deleteMe)
        return response
    } catch (error) {
        return error
    }
}



// Get User One API

export const getOneUser = async (id) => {

    const getOneUser = {
        endpoint: `/users/${id}`,
        type: apiType.get
    }

    try {
        const response = await sendAPIRequest(getOneUser)
        return response
    } catch (error) {
        return error
    }
}


// Get Followers List

export const followersList = async (params) => {
    const followers = {
        endpoint: "/friends/followers",
        type: apiType.get
    }
    try {
        const response = await sendAPIRequest(followers, params)
        return response
    } catch (error) {
        return error
    }
}

// Get Following List

export const followingList = async (params) => {
    const following = {
        endpoint: "/friends/followings",
        type: apiType.get
    }
    try {
        const response = await sendAPIRequest(following, params)
        return response
    } catch (error) {
        return error
    }
}


// block User 

export const blockUserAPI = async (data) => {

    let blockAPI = {
        endpoint: `/users/block/${data?.id}`,
        type: apiType.patch
    }
    try {
        const response = await sendAPIRequest(blockAPI)
        return response
    } catch (error) {
        return error
    }
}
// Follow User 

export const followUser = async (id) => {

    let followAPI = {
        endpoint: `/friends/follow/user/${id}`,
        type: apiType.post
    }
    try {
        const response = await sendAPIRequest(followAPI)
        return response
    } catch (error) {
        return error
    }
}
// UnFollow User 

export const unFollowUser = async (id) => {
    const unFollow_friend = {
        endpoint: `/friends/user/${id}?unfollow=1`,
        type: apiType.delete
    }
    try {
        const response = await sendAPIRequest(unFollow_friend)
        return response
    } catch (error) {
        return error
    }
}
export const removeFollowUser = async (id) => {
    const unFollow_friend = {
        endpoint: `/friends/user/${id}`,
        type: apiType.delete
    }
    try {
        const response = await sendAPIRequest(unFollow_friend)
        return response
    } catch (error) {
        return error
    }
}



// Explore Post API

export const getAllExplorePost = async (params) => {
    const getAll = {
        endpoint: "/posts/explore",
        type: apiType.get
    }

    try {
        const response = await sendAPIRequest(getAll, params)
        return response
    } catch (error) {
        return error
    }
}
export const getAllPost = async (params) => {

    const getAll = {
        endpoint: "/posts",
        type: apiType.get
    }
    try {
        const response = await sendAPIRequest(getAll, params)
        return response
    } catch (error) {
        return error
    }
}
export const addPost = async (payload) => {


    const add = {
        endpoint: "/posts",
        type: apiType.post
    }
    try {
        const response = await sendAPIRequest(add, payload)
        return response
    } catch (error) {
        return error
    }
}
export const getOnePost = async (params) => {

    const getOne = {
        endpoint: `/posts/${params?.id}`,
        type: apiType.get
    }

    try {
        const response = await sendAPIRequest(getOne)
        return response
    } catch (error) {
        return error
    }
}
export const editPost = async (payload) => {

    const updateOne = {
        endpoint: `/posts/${payload?.id}`,
        type: apiType.patch
    }

    delete payload?.id;

    try {
        const response = await sendAPIRequest(updateOne, payload)
        return response
    } catch (error) {
        return error
    }
}
export const deletePost = async (params) => {

    const deleteOne = {
        endpoint: `/posts/${params?.id}`,
        type: apiType.delete
    }

    try {
        const response = await sendAPIRequest(deleteOne)
        return response
    } catch (error) {
        return error
    }
}

export const bookMarkList = async (params) => {
    const getAll = {
        endpoint: `/bookmarks`,
        type: apiType.get
    }

    try {
        const response = await sendAPIRequest(getAll, params)
        return response
    } catch (error) {
        return error
    }
}

export const pinPost = async (id) => {

    const update = {
        endpoint: `/posts/pin/${id}`,
        type: apiType.patch
    }
    try {
        const response = await sendAPIRequest(update)
        return response
    } catch (error) {
        return error
    }
}
export const likePostUserList = async (data) => {

    const like = {
        endpoint: `/post-likes/post/${data?.id}`,
        type: apiType.get
    }
    try {
        const response = await sendAPIRequest(like)
        return response
    } catch (error) {
        return error
    }
}
export const updatePostLike = async (id) => {

    const likeUpdate = {
        endpoint: `/post-likes/post/${id}`,
        type: apiType.post
    }
    try {
        const response = await sendAPIRequest(likeUpdate)
        return response
    } catch (error) {
        return error
    }
}
export const updatePostWishList = async (id) => {
    const wishListUpdate = {
        endpoint: `/bookmarks/post/${id}`,
        type: apiType.post
    }
    try {
        const response = await sendAPIRequest(wishListUpdate)
        return response
    } catch (error) {
        return error
    }
}

export const getCommentList = async (id) => {
    const getComments = {
        endpoint: `/post-comments/post/${id}`,
        type: apiType.get
    }
    try {
        const response = await sendAPIRequest(getComments)
        return response
    } catch (error) {
        return error
    }
}
export const addComments = async (payload) => {

    const add = {
        endpoint: `/post-comments/post/${payload?.id}`,
        type: apiType.post
    }
    delete payload?.id;

    try {
        const response = await sendAPIRequest(add, payload)
        return response
    } catch (error) {
        return error
    }
}
export const deleteComments = async (payload) => {

    const deleteAPI = {
        endpoint: `/post-comments/${payload?.id}`,
        type: apiType.delete
    }

    try {
        const response = await sendAPIRequest(deleteAPI)
        return response
    } catch (error) {
        return error
    }
}

// Projects API

export const getProject = async (payload) => {
    const getAll = {
        endpoint: "/projects",
        type: apiType.get
    }
    try {

        const response = await sendAPIRequest(getAll, payload)
        return response
    } catch (error) {
        return error;
    }
};
export const addProject = async (payload) => {
    const add = {
        endpoint: "/projects",
        type: apiType.post
    }
    try {

        const response = await sendAPIRequest(add, payload)
        return response
    } catch (error) {
        return error;
    }
};


export const getOneProject = async (payload) => {
    const getOne = {
        endpoint: `/projects/${payload?.id}`,
        type: apiType.get
    }
    try {

        const response = await sendAPIRequest(getOne)
        return response
    } catch (error) {
        return error;
    }
};
export const updateProject = async (payload) => {
    const update = {
        endpoint: `/projects/${payload?.id}`,
        type: apiType.patch
    }
    delete payload?.id;
    try {

        const response = await sendAPIRequest(update, payload)
        return response
    } catch (error) {
        return error;
    }
};
export const deleteProject = async (payload) => {
    const deleteProjectAPI = {
        endpoint: `/projects/${payload?.id}`,
        type: apiType.delete
    }
    delete payload?.id;
    try {

        const response = await sendAPIRequest(deleteProjectAPI)
        return response
    } catch (error) {
        return error;
    }
};
export const followOrUnfollowProject = async (params) => {
    const projectFollowUnfollow = {
        endpoint: `/project-followers/project/${params?.id}`,
        type: params?.isFollow ? apiType.post : apiType.delete
    }

    try {

        const response = await sendAPIRequest(projectFollowUnfollow)
        return response
    } catch (error) {
        return error;
    }
};

export const getSubProjectsFromProjectIds = async (params) => {
    const get = {
        endpoint: `/projects/sub-projects`,
        type: apiType.get
    }

    try {

        const response = await sendAPIRequest(get, params)
        return response
    } catch (error) {
        return error;
    }
};




// Community API

export const addCommunity = async (payload) => {
    const add = {
        endpoint: "/communities",
        type: "POST"
    }

    try {
        const response = await sendAPIRequest(add, payload)
        return response
    } catch (error) {
        return error
    }
}

export const joinCommunity = async (payload) => {
    const joinAPI = {
        endpoint: `/community-members/${payload?.id}`,
        type: "POST"
    }

    // delete payload?.id;

    try {
        const response = await sendAPIRequest(joinAPI)
        return response
    } catch (error) {
        return error
    }
}

export const getCommunity = async (payload) => {
    const get = {
        endpoint: "/communities",
        type: "GET"
    }

    try {
        const response = await sendAPIRequest(get, payload)
        return response
    } catch (error) {
        return error
    }
}
export const getOneCommunity = async (id) => {
    const getOneDataAPI = {
        endpoint: `/communities/${id}`,
        type: apiType.get
    }

    try {
        const response = await sendAPIRequest(getOneDataAPI)
        return response
    } catch (error) {
        return error
    }
}
export const deleteCommunity = async (id) => {
    const deleteDataAPI = {
        endpoint: `/communities/${id}`,
        type: apiType.delete
    }

    try {
        const response = await sendAPIRequest(deleteDataAPI)
        return response
    } catch (error) {
        return error
    }
}
export const editCommunity = async (payload) => {
    const updateDataAPI = {
        endpoint: `/communities/${payload?.id}`,
        type: apiType.patch
    }

    // Remove the id from payload
    delete payload?.id;

    try {
        const response = await sendAPIRequest(updateDataAPI, payload)
        return response
    } catch (error) {
        return error
    }
}

// Community Member List
export const communityMemberList = async (params) => {
    try {
        const getAllMembers = {
            endpoint: `/community-members/${params?.id}`,
            type: apiType.get
        }
        delete params?.id
        const response = await sendAPIRequest(getAllMembers, params)
        return response
    } catch (error) {
        return error
    }
}

// ----------------> Todos API  <--------------------

export const addTodo = async (payload) => {
    const add = {
        endpoint: "/todos",
        type: apiType.post
    }

    try {
        const response = await sendAPIRequest(add, payload)
        return response
    } catch (error) {
        return error
    }
}
export const getTodos = async (params) => {
    const get = {
        endpoint: "/todos",
        type: apiType.get
    }


    try {
        const response = await sendAPIRequest(get, params)
        return response
    } catch (error) {
        return error
    }
}
export const getOneTodo = async (id) => {
    const getOne = {
        endpoint: `/todos/${id}`,
        type: apiType.get
    }

    try {
        const response = await sendAPIRequest(getOne)
        return response
    } catch (error) {
        return error
    }
}
export const deleteTodos = async (id) => {
    const deleteDataAPI = {
        endpoint: `/todos/${id}`,
        type: apiType.delete
    }

    try {
        const response = await sendAPIRequest(deleteDataAPI)
        return response
    } catch (error) {
        return error
    }
}
export const updateTodoStatus = async (payload) => {
    const updateDataAPI = {
        endpoint: `/todos/status/${payload?.id}`,
        type: apiType.patch
    }

    // Remove the id from payload
    delete payload?.id;

    try {
        const response = await sendAPIRequest(updateDataAPI)
        return response
    } catch (error) {
        return error
    }
}
export const editTodo = async (payload) => {
    const updateDataAPI = {
        endpoint: `/todos/${payload?.id}`,
        type: apiType.patch
    }

    // Remove the id from payload
    delete payload?.id;

    try {
        const response = await sendAPIRequest(updateDataAPI, payload)
        return response
    } catch (error) {
        return error
    }
}
export const getTeamUsers = async (params) => {
    const get = {
        endpoint: `/users/search`,
        type: apiType.get
    }


    try {
        const response = await sendAPIRequest(get, params)
        return response
    } catch (error) {
        return error
    }
}


// -------------------> Files Folder API <-------------------
export const addFolder = async (payload) => {
    const add = {
        endpoint: "/folders",
        type: apiType.post
    }

    try {
        const response = await sendAPIRequest(add, payload)
        return response
    } catch (error) {
        return error
    }
}
export const getFolders = async (params) => {
    const get = {
        endpoint: "/folders",
        type: apiType.get
    }


    try {
        const response = await sendAPIRequest(get, params)
        return response
    } catch (error) {
        return error
    }
}
export const getOneFolders = async (params) => {
    const getOne = {
        endpoint: `/folders/${params?.id}`,
        type: apiType.get
    }


    try {
        const response = await sendAPIRequest(getOne)
        return response
    } catch (error) {
        return error
    }
}

export const deleteFolder = async (id) => {
    const deleteDataAPI = {
        endpoint: `/folders/${id}`,
        type: apiType.delete
    }

    try {
        const response = await sendAPIRequest(deleteDataAPI)
        return response
    } catch (error) {
        return error
    }
}
export const updateFolder = async (payload) => {
    const updateDataAPI = {
        endpoint: `/folders/${payload?.id}`,
        type: apiType.patch
    }

    // Remove the id from payload
    delete payload?.id;

    try {
        const response = await sendAPIRequest(updateDataAPI, payload)
        return response
    } catch (error) {
        return error
    }
}
// -------------------> Files API <-------------------
export const addFile = async (payload) => {
    const add = {
        endpoint: "/files",
        type: apiType.post
    }

    try {
        const response = await sendAPIRequest(add, payload)
        return response
    } catch (error) {
        return error
    }
}
export const getFiles = async (params) => {
    const get = {
        endpoint: "/files",
        type: apiType.get
    }


    try {
        const response = await sendAPIRequest(get, params)
        return response
    } catch (error) {
        return error
    }
}


export const deleteFiles = async (id) => {
    const deleteDataAPI = {
        endpoint: `/files/${id}`,
        type: apiType.delete
    }

    try {
        const response = await sendAPIRequest(deleteDataAPI)
        return response
    } catch (error) {
        return error
    }
}
export const updateFiles = async (payload) => {
    const updateDataAPI = {
        endpoint: `/files/${payload?.id}`,
        type: apiType.patch
    }

    // Remove the id from payload
    delete payload?.id;

    try {
        const response = await sendAPIRequest(updateDataAPI, payload)
        return response
    } catch (error) {
        return error
    }
}

// -------------------> Notification API <-------------------
export const getNotification = async (params) => {
    const get = {
        endpoint: "/notifications",
        type: apiType.get
    }


    try {
        const response = await sendAPIRequest(get, params)
        return response
    } catch (error) {
        return error
    }
}
export const updateMemberStatus = async (payload) => {
    const update = {
        endpoint: `/project-members/accept/${payload?.id}`,
        type: apiType.patch
    }
    delete payload?.id;
    // console.log(pay)
    try {
        const response = await sendAPIRequest(update, payload)
        return response
    } catch (error) {
        return error
    }
}
export const updateMemberCommunityPostStatus = async (payload) => {
    const update = {
        endpoint: `posts/approve/${payload?.id}`,
        type: apiType.patch
    }
    delete payload?.id;
    try {
        const response = await sendAPIRequest(update, payload)
        return response
    } catch (error) {
        return error
    }
}

// -------------------> Team API <-------------------
export const getAllUser = async (params) => {
    const get = {
        endpoint: "/project-members/users",
        type: apiType.get
    }


    try {
        const response = await sendAPIRequest(get, params)
        return response
    } catch (error) {
        return error
    }
}
export const getTeam = async (params) => {
    const get = {
        endpoint: `/project-members/projects/${params?.id}`,
        type: apiType.get
    }
    delete params?.id;

    try {
        const response = await sendAPIRequest(get, params)
        return response
    } catch (error) {
        return error
    }
}
export const addTeam = async (payload) => {
    const add = {
        endpoint: "/project-members",
        type: apiType.post
    }


    try {
        const response = await sendAPIRequest(add, payload)
        return response
    } catch (error) {
        return error
    }
}
export const updateTeamAccess = async (payload) => {
    const updateData = {
        endpoint: `/project-members/${payload?.id}`,
        type: apiType.patch
    }

    delete payload?.id;

    try {
        const response = await sendAPIRequest(updateData, payload)
        return response
    } catch (error) {
        return error
    }
}
export const deleteTeamMember = async (payload) => {
    const deleteData = {
        endpoint: `/project-members/${payload?.id}`,
        type: apiType.delete
    }


    try {
        const response = await sendAPIRequest(deleteData)
        return response
    } catch (error) {
        return error
    }
}

// -------------------> Stories API <-------------------

export const getAllStories = async (params) => {
    const get = {
        endpoint: `/stories/`,
        type: apiType.get
    }


    try {
        const response = await sendAPIRequest(get)
        return response
    } catch (error) {
        return error
    }
}
export const getUserStories = async (params) => {
    const get = {
        endpoint: `/stories/users/${params?.id}`,
        type: apiType.get
    }


    try {
        const response = await sendAPIRequest(get)
        return response
    } catch (error) {
        return error
    }
}
export const addHighlights = async (payload) => {
    const add = {
        endpoint: `/highlights`,
        type: apiType.post
    }


    try {
        const response = await sendAPIRequest(add, payload)
        return response
    } catch (error) {
        return error
    }
}
export const updateHighlights = async (payload) => {
    const update = {
        endpoint: `/highlights/${payload?.id}`,
        type: apiType.patch
    }

    delete payload?.id;

    try {
        const response = await sendAPIRequest(update, payload)
        return response
    } catch (error) {
        return error
    }
}
export const deleteHighlights = async (payload) => {
    const deleteHighlight = {
        endpoint: `/highlights/${payload?.id}`,
        type: apiType.delete
    }


    try {
        const response = await sendAPIRequest(deleteHighlight)
        return response
    } catch (error) {
        return error
    }
}
export const getAllHighlights = async (params) => {
    const get = {
        endpoint: `/highlights/`,
        type: apiType.get
    }


    try {
        const response = await sendAPIRequest(get, params)
        return response
    } catch (error) {
        return error
    }
}

// -------------------> Report Post,Project,Community API <-------------------
export const reportPost = async (payload) => {
    const add = {
        endpoint: `/reported-posts/post/${payload?.id}`,
        type: apiType.post
    }
    delete payload?.id;

    try {
        const response = await sendAPIRequest(add, payload)
        return response
    } catch (error) {
        return error
    }
}
export const reportProject = async (payload) => {
    const add = {
        endpoint: `/reported-projects/project/${payload?.id}`,
        type: apiType.post
    }

    delete payload?.id;

    try {
        const response = await sendAPIRequest(add, payload)
        return response
    } catch (error) {
        return error
    }
}
export const reportCommunity = async (payload) => {
    const add = {
        endpoint: `/reported-communities/community/${payload?.id}`,
        type: apiType.post
    }
    delete payload?.id;

    try {
        const response = await sendAPIRequest(add, payload)
        return response
    } catch (error) {
        return error
    }
}
