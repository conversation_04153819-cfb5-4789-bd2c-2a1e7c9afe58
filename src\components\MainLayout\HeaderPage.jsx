"use client";
import React, { useContext, useEffect, useRef, useState } from "react";
import logo from "../../../public/images/logo/logo.png";
import Image from "next/image";
import authStorage from "@/utils/API/AuthStorage";
import { valContext } from "../context/ValContext";
import { deleteMyAccount, getMyProfile } from "@/app/action";
import {
  AboutUsIcon,
  ChangePasswordIcon,
  ChangeThemeIcon,
  CloseModalIcon,
  CustomizeHeaderIcon,
  LeftArrowBackIcon,
  NotificationIcon,
  PrivacyPolicyIcon,
  ProfileIcon,
  SignOutIcon,
  TermsIcon,
} from "@/utils/icons";
import { SearchIcon, Trash, Trash2, X } from "lucide-react";
import { usePathname, useRouter } from "next/navigation";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Link from "next/link";
import { getToken, routes } from "@/utils/function";
import PopUpModal from "../Common/PopUpModal";
import CustomizeNavigation from "../Profile/CustomizeNavigation";
import PasswordNavigation from "../Profile/PasswordNavigation";
import useApiRequest from "../helper/hook/useApiRequest";

import toast from "react-hot-toast";
import { setAllowToasts } from "@/utils/safeToast";
import UserAvatar from "../Common/UserAvatar";
import WebAppLogo from "../Common/WebAppLogo";
import { useDebouncedSearch } from "../helper/hook/useDebouncedSearch";
import SearchBar from "../Common/SearchBar";

const HeaderPage = ({
  isClose,
  children,
  searchValue,
  setSearchValue,
  setGlobalSearch,
}) => {
  const [profileData, setProfileData] = useState({});
  const [isLogOut, setIsLogOut] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleteAccount, setIsDeleteAccount] = useState(false);
  const [isCustomizeNavigationOpen, setIsCustomizeNavigationOpen] =
    useState(false);
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false);
  const router = useRouter();
  const pathName = usePathname();

  const api = useApiRequest();
  const singleApi = useApiRequest(false);
  const {
    updatedProfile,
    setIsNewNotifications,
    isNewNotifications,
    setSearchData,
    setSearchPagination,
  } = useContext(valContext);
  const debounceRef = useRef(null);

  // console.log(profileData);

  const resetData = () => {
    setSearchData([]);
    setSearchPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
  };

  const onSearch = useDebouncedSearch({
    setSearchQuery: setGlobalSearch, // required
    resetData, // optional
    setSearch: setSearchValue, // optional
    isNeedToStore: true,
  });

  const headerMenus = [
    {
      id: 1,
      label: "Profile",
      icon: <ProfileIcon size={16} />,
      onClick: () => {
        setTimeout(() => {
          setSearchValue("");
          setGlobalSearch("");
        }, 1000);
        router.push("/profile");
      },
    },
    {
      id: 2,
      label: "Change Password",
      icon: <ChangePasswordIcon size={16} />,
      onClick: () => {
        // router.push("/profile");
        setIsPasswordModalOpen((prev) => !prev);
      },
    },
    // {
    //   id: 3,
    //   label: "Customize Header",
    //   icon: <CustomizeHeaderIcon size={16} />,
    //   onClick: () => {
    //     setIsCustomizeNavigationOpen((prev) => !prev);
    //   },
    // },
    // {
    //   id: 4,
    //   label: "Change Theme",
    //   icon: <ChangeThemeIcon size={16} />,
    //   onClick: () => {
    //     // router.push("/profile");
    //   },
    // },
    {
      id: 5,
      label: "About Us",
      icon: <AboutUsIcon size={16} />,
      onClick: () => {
        setTimeout(() => {
          setSearchValue("");
          setGlobalSearch("");
        }, 1000);
        router.push("/about");
      },
    },
    {
      id: 6,
      label: "Terms of Service",
      icon: <TermsIcon size={16} />,
      onClick: () => {
        // window.open("https://www.incenti.ai/terms", "_blank");

        setTimeout(() => {
          setSearchValue("");
          setGlobalSearch("");
        }, 1000);
        router.push("/terms-and-condition");
      },
    },
    {
      id: 7,
      label: "Privacy Policy",
      icon: <PrivacyPolicyIcon size={16} />,
      onClick: () => {
        // window.open("https://www.incenti.ai/privacy-policy", "_blank");

        setTimeout(() => {
          setSearchValue("");
          setGlobalSearch("");
        }, 1000);
        router.push("/privacy-policy");
      },
    },
    {
      id: 8,
      label: "Delete Account",
      icon: <Trash2 stroke="#6D11D2" size={16} />,
      onClick: () => {
        setIsDeleteAccount(true);
      },
    },
    {
      id: 9,
      label: "Sign Out",
      icon: <SignOutIcon size={16} />,
      onClick: () => {
        // toast.remove();
        // toast.dismiss();

        setIsLogOut(true);
      },
    },
  ];

  useEffect(() => {
    if (!getToken()?.token) {
      router.push("/login");
      return;
    }

    api.sendRequest(getMyProfile, (res) => {
      setProfileData(res?.data);
      // res?.newNotifications;
      setIsNewNotifications(res?.data?.newNotifications);
      // console.log(res?.data);
      if (res?.data) {
        authStorage.setProfileDetails(res?.data);
      }
    });
  }, []);

  // useEffect(() => {
  //   if (!pathName.includes("/search")) {
  // setSearchValue("");
  // setGlobalSearch("");
  //   }
  // }, [pathName]);

  return (
    <>
      {/* Change Password */}
      <PasswordNavigation
        isOpen={isPasswordModalOpen}
        setIsOpen={setIsPasswordModalOpen}
      />
      {/* Customize Header Modal */}
      <CustomizeNavigation
        isOpen={isCustomizeNavigationOpen}
        setIsOpen={setIsCustomizeNavigationOpen}
        data={profileData?.customSetting?.homeSection}
        setProfileData={setProfileData}
      />
      {/* SignOut Modal */}
      <PopUpModal
        isLoading={isLoading}
        isOpen={isLogOut}
        setIsOpen={setIsLogOut}
        mainMessage="Sign Out"
        icon={<SignOutIcon size={50} />}
        iconBgClass="tw-rounded-full tw-p-5 tw-bg-[#6D11D214]"
        subMessage="Would you like to sign out of your account?"
        cancelButtonText="Not Now"
        confirmButtonText="Yes,Sign Out"
        onConfirm={() => {
          setIsLoading(true);
          setAllowToasts(false);
          window.setTimeout(() => {
            authStorage.deleteAuthDetails();
            router.push("/login");
          }, 200);
        }}
      />
      {/* Delete Modal */}
      <PopUpModal
        isLoading={singleApi.isLoading}
        isOpen={isDeleteAccount}
        setIsOpen={setIsDeleteAccount}
        mainMessage="Delete Account"
        iconBgClass="tw-rounded-full tw-p-5 tw-bg-[#6D11D214]"
        subMessage="If you delete your account, all your information will be removed from the platform Permanently."
        cancelButtonText="Not Now"
        confirmButtonText="Yes,Delete"
        onConfirm={() => {
          setAllowToasts(false);
          singleApi.sendRequest(deleteMyAccount, () => {
            authStorage.deleteAuthDetails();
            router.push("/login");
          });
        }}
      />
      <header className="tw-bg-white tw-pb-4 tw-pt-6 tw-flex tw-items-center tw-justify-between md:tw-px-24 tw-px-[0.9375rem]">
        <div
          className={`tw-flex tw-items-center tw-flex-1 ${
            isClose ? "" : "lg:tw-justify-end tw-justify-between"
          } `}
        >
          {/* {children} */}
          {!pathName.includes(routes.SEARCH) && (
            <Link className="lg:tw-hidden" href={routes.HOME}>
              <WebAppLogo />
            </Link>
          )}
          <div
            className={`tw-flex tw-items-center tw-justify-between md:tw-gap-x-5 tw-gap-x-[0.9375rem]  tw-transition-all tw-duration-500 tw-ease-in-out  ${
              pathName.includes(routes.SEARCH) ? "tw-w-full" : "tw-w-auto"
            }`}
          >
            {!pathName.includes(routes.SEARCH) ? (
              <Link
                href={routes.SEARCH}
                className={`tw-flex tw-gap-2 tw-items-center tw-rounded-full `}
              >
                <SearchIcon color="#2D394A" />
              </Link>
            ) : (
              <div className="tw-flex tw-gap-x-[0.9375rem] tw-items-center lg:tw-block lg:tw-w-full">
                <button
                  type="button"
                  onClick={() => {
                    // progress.start(0, 1);

                    router.back();
                  }}
                  className="lg:tw-hidden"
                >
                  <LeftArrowBackIcon />
                </button>
                <SearchBar
                  parentClassName="!tw-w-full !tw-my-0"
                  search={searchValue}
                  setSearch={setSearchValue}
                  setSearchQuery={setGlobalSearch}
                  onSearch={onSearch}
                  resetData={resetData}
                />
              </div>
            )}
            <div className="tw-relative">
              <Link
                href={"/notifications"}
                className="tw-font-bold  tw-cursor-pointer "
                onClick={() => {
                  setTimeout(() => {
                    setSearchValue("");
                    setGlobalSearch("");
                  }, 1000);
                }}
              >
                <NotificationIcon />
                {/* tw-bg-[#EF3B41] */}
                {isNewNotifications && (
                  <div className="tw-absolute tw-top-0 tw-right-0 tw-z-10 tw-bg-primary-purple tw-h-3 tw-w-3 tw-rounded-full tw-border-2 tw-border-white" />
                )}
              </Link>
            </div>
            {/* Profile DropDown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <div className="tw-cursor-pointer">
                  <UserAvatar
                    imageParentClassName={`${
                      pathName.includes("/search")
                        ? "!tw-w-11 !tw-h-11"
                        : "!tw-w-10 !tw-h-10"
                    } `}
                    imageUrl={updatedProfile ?? profileData?.image}
                    userName={profileData?.firstName}
                    userNameClassName={"!tw-left-[50%] "}
                  />
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="end"
                className="!tw-rounded-2xl !tw-px-0 !tw-pt-5 !tw-pb-3 !tw-w-[18rem] !tw-z-[100]"
              >
                {headerMenus
                  ?.filter((ele) =>
                    profileData?.isPasswordSet
                      ? ele
                      : ele?.label !== "Change Password"
                  )
                  ?.map((ele) => (
                    <DropdownMenuItem
                      onClick={() => {
                        ele.onClick();
                      }}
                      className="tw-mb-1.5 tw-px-5 tw-cursor-pointer"
                      key={ele.id}
                    >
                      <div className=" tw-flex tw-gap-3 tw-items-center">
                        <div className="tw-rounded-full tw-p-2.5 tw-bg-[#6D11D214]">
                          {ele.icon}
                        </div>
                        <p className="tw-text-lg">{ele.label}</p>
                      </div>
                    </DropdownMenuItem>
                  ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </header>
    </>
  );
};

export default HeaderPage;
