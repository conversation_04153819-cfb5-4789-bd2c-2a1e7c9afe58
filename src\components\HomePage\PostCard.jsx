"use client";
import Image from "next/image";
import Link from "next/link";
import React, { useContext, useEffect, useMemo, useRef, useState } from "react";
import {
  BlockUserIcon,
  BookmarkFillOutIcon,
  BookmarkIcon,
  DuplicateIcon,
  EditProfileIcon,
  HeartFillOutIcon,
  HeartIcon,
  MeassageFillOutIcon,
  MessageIcon,
  MovePostIcon,
  PinIcon,
  ShareIcon,
  ThreeDotMenuIcon,
} from "@/utils/icons";
import CustomTitle from "../Common/CustomTitle";
import {
  addDataListToOrderedListItems,
  blurDataURL,
  finalDescription,
  formatNumber,
  removeTargetAttributeFromLink,
  RESPONSE_STATUS,
  routes,
} from "@/utils/function";
import moment from "moment";
import { Tooltip, TooltipContent, TooltipTrigger } from "../ui/tooltip";
import CommentDrawer from "./CommentDrawer";
import toast from "react-hot-toast";
import { addComments, deletePost, getOnePost, reportPost } from "@/app/action";
import PostPreview from "./PostPreview";
import PostImageCarousel from "./PostImageCarousel";
import { Flag, Trash2 } from "lucide-react";
import PostModal from "./Form/PostModal";
import PopUpModal from "../Common/PopUpModal";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import useApiRequest from "../helper/hook/useApiRequest";
import ShowMenuList from "../Common/ShowMenuList";
import ShareModal from "../Common/ShareModal";
import ReportModal from "../Common/ReportModal";
import MoveToModal from "./MoveToModal";
import { valContext } from "../context/ValContext";
import { useProgress } from "@bprogress/next";
import { safeToast } from "@/utils/safeToast";
import UserAvatar from "../Common/UserAvatar";
import tempProjectImg from "../../../public/images/assets/default_image.png";
import blockImg from "../../../public/images/assets/block.png";
import tempCommunityImg from "../../../public/images/assets/group-community.jpg";
import FollowingAndFollowersModal from "../Profile/FollowingAndFollowersModal";
import LaptopPostCard from "./Component/LaptopPostCard";
import MobilePostCard from "./Component/MobilePostCard";

const PostCard = ({
  data = {},
  getLikeData,
  getWishlistData,
  getCommentData,
  userData,
  followAndUnfollowHandler,
  setRefresh = () => {},
  refetchData = () => {},
  isUserLinkActive = false,
  isShowFollowButton = true,
  titleForEditPostModal = null,
  CommunityId = null,
  hasEditAccessOnly = false,
  allSettings = false,
  pinPostHandler,
  isProjectRemoveAble = true,
  onMoveHandler,
  origin,
  handlePostClick = () => {},
  isBlockUser = false,
  blockerUser = () => {},
  postForProject = false,
  postForSubProject = false,
}) => {
  const [postState, setPostState] = useState({
    isLiked: +data?.isLiked, // Convert to boolean
    likedCount: +data?.likesCount,
    commentCount: +data?.commentsCount ?? 0,
    isWishListed: +data?.isBookMarked,
    isFollowed: isNaN(+data?.User?.isFollowed) ? 0 : +data?.User?.isFollowed,
    pinPost: data?.pinnedAt ? true : false,
  });
  const [commentModal, setCommentModal] = useState(false);
  const [commentList, setCommentList] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isCommentLoading, setIsCommentLoading] = useState(false);
  const [previewModal, setPreviewModal] = useState(false);
  const [postData, setPostData] = useState(null);
  const [editData, setEditData] = useState(null);
  const [deleteData, setDeleteData] = useState(null);
  const singleOneAPI = useApiRequest(false);
  const [isShareOpen, setIsShareOpen] = useState(false);
  const [isReported, setIsReported] = useState(false);
  const [reportData, setReportData] = useState(null);
  const [isMoveToOpen, setIsMoveToOpen] = useState(false);
  const [openModal, setOpenModal] = useState({
    isOpen: false,
    type: null,
  });
  const [duplicateData, setDuplicateData] = useState(null);
  const [type, setType] = useState({
    isOpen: false,
    type: null,
  });
  const [postId, setPostId] = useState(null);
  const { latestCreatedPost, setLatestCreatedPost, isMobile } =
    useContext(valContext);
  const path = usePathname();
  const router = useRouter();
  const progress = useProgress();

  // console.log(postState);
  const icons = [
    {
      name: "Like",
      icon: postState?.isLiked ? <HeartFillOutIcon /> : <HeartIcon />, // <HeartFillOutIcon />
      count: formatNumber(+postState?.likedCount) || 0,
      onClick: async (id) => {
        setPostState((prev) => ({
          ...prev,
          likedCount: prev.isLiked ? prev.likedCount - 1 : prev.likedCount + 1,
          isLiked: !prev.isLiked,
        }));
        getLikeData(id);
      },
    },
    {
      name: "Comment",
      icon: <MessageIcon />, // <MeassageFillOutIcon />
      count: formatNumber(+postState?.commentCount) || 0,
      onClick: (id) => {
        // getCommentData(id);
        setIsCommentLoading(true);
        setCommentModal(true);
        getCommentData(id, setCommentList, setIsCommentLoading);
      },
    },
    {
      name: "Wishlist",
      icon: postState?.isWishListed ? (
        <BookmarkFillOutIcon />
      ) : (
        <BookmarkIcon />
      ),
      onClick: (id) => {
        setPostState((prev) => ({
          ...prev,
          isWishListed: !prev.isWishListed,
        }));
        getWishlistData(id);
      },
    },
    {
      name: "Share",
      icon: <ShareIcon size={22} stroke="#2D394A" />,
      onClick: (id) => {
        setIsShareOpen(true);
      },
    },
  ];

  const processedDescription = useMemo(() => {
    return data ? finalDescription(data?.description) : "";
  }, [data?.description]);

  // Get One Post Details
  const getPostDetails = (
    id,
    setDataState,
    isForEditData = true,
    execFun = () => {}
  ) => {
    singleOneAPI.sendRequest(
      getOnePost,
      (res) => {
        setDataState(res?.data);
        execFun();
        if (!isForEditData) {
          setPostState((prev) => ({
            ...prev,
            isFollowed: +res?.data?.User?.isFollowed,
          }));
        }
      },
      {
        id,
      }
    );
  };

  const reportPostHandler = (ele) => {
    setIsReported(true);
    setReportData(ele);
  };

  const myPostSetting = [
    {
      label: "Share",
      className: "",
      icon: <ShareIcon size={17} stroke="#2D394A" />,
      onClick: () => {
        setIsShareOpen(true);
      },
    },
    {
      label: `${postState?.pinPost ? "Unpin" : "Pin"} Post`,
      className: "",
      icon: <PinIcon size={17} stroke="#2D394A" />,
      onClick: (ele) => {
        // setPostState((prev) => ({
        //   ...prev,
        //   pinPost: !prev?.pinPost,
        // }));
        pinPostHandler(ele?.id, setPostState);
      },
    },
    {
      label: "Edit",
      className: "",
      icon: <EditProfileIcon size={17} stroke="#2D394A" />,
      onClick: (ele) => {
        getPostDetails(ele?.id, setEditData);
      },
    },
    {
      label: "Move Post to",
      className: "",
      icon: <MovePostIcon size={17} stroke="#2D394A" />,
      onClick: (ele) => {
        setIsMoveToOpen(true);
      },
    },
    {
      label: "Delete",
      className: "tw-text-[#EF3B41]",
      icon: <Trash2 stroke="#EF3B41" size={17} />,
      onClick: (ele) => {
        setDeleteData(ele);
      },
    },
  ];

  const otherProjectSetting = [
    // {
    //   label: "Copy to Edit",
    //   className: "",
    //   icon: <DuplicateIcon size={17} stroke="#2D394A" />,
    //   onClick: (ele) => {
    //     getPostDetails(ele?.id, setDuplicateData, false, () => {
    //       // setIsOpen(true);
    // setOpenModal({
    //   isOpen: true,
    //   type: "post",
    // });
    //     });
    //   },
    // },
    {
      label: "Share",
      className: "",
      icon: <ShareIcon size={17} stroke="#2D394A" />,
      onClick: () => {
        setIsShareOpen(true);
      },
    },
    {
      label: "Report this Post",
      className: "",
      icon: <Flag size={17} stroke="#2D394A" />,
      onClick: (ele) => {
        reportPostHandler(ele);
      },
    },
  ];

  const postNavigation = (slug) => {
    progress.start();
    handlePostClick();
    // router.push(`/posts/${data?.slug}`);
    router.push(routes.SINGLE_POST?.replace(":slug", slug));
  };

  // Comment Submit Handler
  const onCommentSubmitHandler = async (values, resetForm) => {
    // console.log(values, "data");
    setIsLoading(true);
    const payload = {
      comment: values?.comment,
    };

    // console.log(payload);
    try {
      const res = await addComments({
        ...payload,
        id: data?.id,
      });
      // console.log(res);
      if (res?.status !== RESPONSE_STATUS.SUCCESS && res?.status !== 200) {
        throw res;
      }
      resetForm();
      setIsCommentLoading(true);
      setPostState((prev) => ({
        ...prev,
        commentCount: prev.commentCount + 1,
      }));
      getCommentData(data?.id, setCommentList, setIsCommentLoading);
    } catch (err) {
      // console.log(err);
      safeToast.error(err?.message);
    }
    setIsLoading(false);
  };

  const searchParams = useSearchParams();
  const pageQuery = searchParams.get("q");

  useEffect(() => {
    if (pageQuery) {
      getPostDetails(pageQuery, setPostData, false);
    }
  }, [pageQuery]);

  return (
    <>
      <FollowingAndFollowersModal
        // setRefresh={setRefresh}
        postId={postId}
        modalType={type}
        setModalType={setType}
        loginUserData={userData}
        apiKey="likeUser"
        isSearchRequired={false}
      />
      {/* Move to Modal */}
      <MoveToModal
        data={data}
        open={isMoveToOpen}
        setIsOpen={setIsMoveToOpen}
        onMoveHandler={onMoveHandler}
      />
      {/* Report Modal */}
      {/* <ReportModal isOpen={isReported} setIsOpen={setIsReported} /> */}
      <ReportModal
        isOpen={isReported}
        setIsOpen={setIsReported}
        isLoading={singleOneAPI?.isLoading}
        onConfirm={(reason) => {
          const payload = {
            reason,
            id: reportData?.id,
          };
          singleOneAPI.sendRequest(
            reportPost,
            (res) => {
              safeToast.success(res?.message);
              setIsReported(false);
              setRefresh((prev) => !prev);
              refetchData();
              setReportData(null);
            },
            payload
          );
        }}
      />

      <ShareModal
        open={isShareOpen}
        onClose={() => {
          setIsShareOpen(false);
        }}
        shareUrl={`${origin}/posts/${data?.slug}`}
        title={`${data?.title} \nCheckout this post:`}
      />

      {/* Delete Modal */}
      <PopUpModal
        isLoading={singleOneAPI.isLoading}
        isOpen={deleteData}
        setIsOpen={setDeleteData}
        onConfirm={() => {
          singleOneAPI.sendRequest(
            deletePost,
            () => {
              setRefresh((prev) => !prev);
              if (latestCreatedPost?.id === deleteData?.id) {
                setLatestCreatedPost(null);
              }
              refetchData();
              setDeleteData(null);
            },
            {
              id: deleteData?.id,
            },
            "Post Deleted Successfully"
          );
        }}
      />
      {/* Duplicate Post Modal */}
      <PostModal
        open={openModal}
        setOpen={setOpenModal}
        setRefresh={setRefresh}
        reFetchData={refetchData}
        title={null}
        // title={
        //   titleForEditPostModal
        //     ? editData
        //       ? `Edit ${titleForEditPostModal}`
        //       : `Create ${titleForEditPostModal}`
        //     : null
        // }
        duplicateData={duplicateData}
        onlyForPost={true}
        // isRemoveAble={isProjectRemoveAble}
        // CommunityId={CommunityId}
        // showPostVisibility={!CommunityId}
        // showProject={!CommunityId}
        // showSaveAsDraft={!CommunityId}
      />
      {/* Edit Post Modal */}
      <PostModal
        editData={editData}
        setEditData={setEditData}
        setRefresh={setRefresh}
        reFetchData={refetchData}
        title={
          titleForEditPostModal
            ? editData
              ? `Edit ${titleForEditPostModal}`
              : `Create ${titleForEditPostModal}`
            : null
        }
        CommunityId={CommunityId}
        showPostVisibility={!CommunityId}
        showProject={!CommunityId}
        showSaveAsDraft={!CommunityId}
        isRemoveAble={isProjectRemoveAble}
        postForProject={postForProject}
        postForSubProject={postForSubProject}
      />
      {/* Comment Drawer */}
      <CommentDrawer
        loginUserData={userData}
        postData={data}
        isOpen={commentModal}
        setIsOpen={setCommentModal}
        setCommentList={setCommentList}
        isCommentLoading={isCommentLoading}
        isLoading={isLoading}
        onCommentSubmit={onCommentSubmitHandler}
        commentList={commentList}
        setPostState={setPostState}
      />
      {/* Preview Modal */}
      <PostPreview
        isCurrentUser={userData?.id !== postData?.User?.id}
        isOpen={previewModal}
        // isOpen={pageQuery === data?.slug}
        setIsOpen={setPreviewModal}
        isShowFollowButton={isShowFollowButton}
        data={postData}
        setData={setPostData}
        isLoading={singleOneAPI.isLoading}
        getLikeData={(id) => {
          getLikeData(id);
        }}
        getWishlistData={(id) => {
          getWishlistData(id);
        }}
        commentHandler={(id) => {
          setIsCommentLoading(true);
          setCommentModal(true);
          getCommentData(id, setCommentList, setIsCommentLoading);
        }}
        postState={postState}
        setPostState={setPostState}
        followAndUnfollowHandler={(userId, isFollow) => {
          followAndUnfollowHandler(userId, isFollow);
        }}
        router={router}
        path={path}
        setIsShareOpen={setIsShareOpen}
        reportPostHandler={reportPostHandler}
      />
      <div className="" key={`post-${data?.slug}`}>
        {/* Mobile View */}
        <>
          <MobilePostCard
            data={data}
            postNavigation={postNavigation}
            path={path}
            postState={postState}
            progress={progress}
            isUserLinkActive={isUserLinkActive}
            icons={icons}
            setPostId={setPostId}
            setType={setType}
            otherProjectSetting={otherProjectSetting}
            blockerUser={blockerUser}
            setRefresh={setRefresh}
            refetchData={refetchData}
            blockImg={blockImg}
            tempProjectImg={tempProjectImg}
            tempCommunityImg={tempCommunityImg}
            isBlockUser={isBlockUser}
            getPostDetails={getPostDetails}
            setEditData={setEditData}
            hasEditAccessOnly={hasEditAccessOnly}
            myPostSetting={myPostSetting}
            CommunityId={CommunityId}
            allSettings={allSettings}
            userData={userData}
            pinPostHandler={pinPostHandler}
            router={router}
            description={processedDescription}
          />
        </>
        {/* PC View */}
        <>
          <LaptopPostCard
            data={data}
            postNavigation={postNavigation}
            path={path}
            postState={postState}
            progress={progress}
            isUserLinkActive={isUserLinkActive}
            icons={icons}
            setPostId={setPostId}
            setType={setType}
            otherProjectSetting={otherProjectSetting}
            blockerUser={blockerUser}
            setRefresh={setRefresh}
            refetchData={refetchData}
            blockImg={blockImg}
            tempProjectImg={tempProjectImg}
            tempCommunityImg={tempCommunityImg}
            isBlockUser={isBlockUser}
            getPostDetails={getPostDetails}
            setEditData={setEditData}
            hasEditAccessOnly={hasEditAccessOnly}
            myPostSetting={myPostSetting}
            CommunityId={CommunityId}
            allSettings={allSettings}
            userData={userData}
            pinPostHandler={pinPostHandler}
            router={router}
            description={processedDescription}
          />
        </>
      </div>
    </>
  );
};

export default PostCard;
