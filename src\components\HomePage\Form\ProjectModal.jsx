import { CloseModalIcon, ProjectImageUploadIcon } from "@/utils/icons";
import { useFormik } from "formik";
import { ChevronDown, ChevronUp } from "lucide-react";
import { useEffect, useState } from "react";
import Modal from "react-responsive-modal";
import SubProjectModal from "./SubProjectModal";
import CustomButton from "@/components/Common/Custom-Button";
import FloatingLabelInput from "./FloatingLabelInput";
import FloatingLabelTextArea from "./FloatingLabelTextArea";
import GooglePlacesAutocomplete from "react-google-places-autocomplete";
import Image from "next/image";
import toast from "react-hot-toast";
import { isValidURL, RESPONSE_STATUS } from "@/utils/function";
import { addProject, updateImageToURL, updateProject } from "@/app/action";
import * as Yup from "yup";
import "react-responsive-modal/styles.css";
import useApiRequest from "@/components/helper/hook/useApiRequest";
import { useRouter } from "next/navigation";
import { safeToast } from "@/utils/safeToast";

const ProjectModal = ({
  isInHomePage = false,
  open,
  setOpen,
  setRefresh = () => {},
  reFetchData = () => {},
  editData = null,
  setEditData = () => {},
  modalTitle,
  modalSubmitButton,
}) => {
  const [isAddMore, setIsAddMore] = useState(false);
  const [isSubProject, setIsSubProject] = useState(false);
  const [subProjectList, setSubProjectList] = useState([]);
  const [selectedImage, setSelectedImage] = useState(null);
  const [previewImage, setPreviewImage] = useState(null);
  const [isFocused, setIsFocused] = useState(false);
  const [location, setLocation] = useState(null);
  const [editSubProjectData, setEditSubProjectData] = useState(null);
  const [defaultVisibility, setDefaultVisibility] = useState("Public");
  const [isLoading, setIsLoading] = useState(false);
  const api = useApiRequest();
  const router = useRouter();

  // Reset Modal
  const resetModal = () => {
    if (editData) {
      setEditData(null);
    } else {
      setOpen({
        type: null,
        isOpen: false,
      });
    }
    formik.resetForm();
    setIsAddMore(false);
    setIsSubProject(false);
    setSubProjectList([]);
    setSelectedImage(null);
    setPreviewImage(null);
    setIsFocused(null);
    setLocation(null);
    setEditSubProjectData(null);
    setIsLoading(false);
  };
  // validationSchema;
  const validationSchema = Yup.object().shape({
    name: Yup.string().trim().required("Project Name is required"),
  });

  // console.log(subProjectList);
  // Form Submit Handler
  const formSubmitHandler = async (values) => {
    setIsLoading(true);
    // console.log("Updated Profile:", values);
    let payload = {};
    Object.keys(values)?.forEach((ele) => {
      if (ele === "isPrivate") {
        payload = {
          ...payload,
          isPrivate: values[ele] === "Private",
        };
      } else {
        payload = {
          ...payload,
          [ele]: values[ele]?.trim() === "" ? null : values[ele]?.trim(),
        };
      }
    });

    payload = {
      ...payload,
      location: location && location?.label !== "" ? location?.label : null,
    };

    if (selectedImage) {
      if (isValidURL(selectedImage)) {
        payload = {
          ...payload,
          image: selectedImage,
        };
      } else {
        // Upload from device
        const formData = new FormData();
        formData.append("file", selectedImage);

        try {
          const res = await updateImageToURL(formData);
          if (res?.status !== 200 && res?.status !== RESPONSE_STATUS.SUCCESS) {
            throw res;
          }

          setSelectedImage(res?.data?.[0]?.link);
          payload = {
            ...payload,
            image: res?.data?.[0]?.link,
          };
        } catch (error) {
          console.dir(error, "error");
          return;
        }
      }
    } else if (editData && !selectedImage) {
      payload = {
        ...payload,
        image: null,
      };
    }
    if (subProjectList?.length > 0 && !editData) {
      payload = {
        ...payload,
        subProjects: subProjectList?.map((ele) => ({
          name: ele?.name,
          isPrivate: payload?.isPrivate ? true : ele?.isPrivate,
          description: ele?.description,
          location: ele?.location,
          image: ele?.image,
        })),
      };
    }

    // console.log("Updated Values:", payload);

    if (editData) {
      api.sendRequest(
        updateProject,
        (res) => {
          resetModal();
          // setRefresh((prev) => !prev);
          setRefresh((prev) => (typeof prev === "undefined" ? true : !prev));
          reFetchData();
        },
        {
          ...payload,
          id: editData?.id,
        },
        "Project Updated Successfully",
        () => {
          // Error Handler
          setIsLoading(false);
        }
      );
    } else {
      // Create the New Project
      api.sendRequest(
        addProject,
        (res) => {
          if (isInHomePage && !editData) {
            router.push("/projects");
          }
          resetModal();
          setRefresh((prev) => !prev);
          reFetchData();
          safeToast.success(res?.message);
        },
        payload,
        "",
        (err) => {
          // Error Handler
          // console.log(err);
          safeToast.error(err?.message);
          setIsLoading(false);
        }
      );
    }
  };
  // Formik
  const formik = useFormik({
    enableReinitialize: true, // Allows form values to update when `open` changes
    initialValues: {
      name: "",
      isPrivate: "Public",
    },
    validationSchema,
    onSubmit: async (values) => {
      // console.log("Updated Profile:", values);

      formSubmitHandler(values);
    },
  });

  // Preview Image
  const handleProfileImage = (e) => {
    if (!e.target.files[0] || !e.target.files[0].type.startsWith("image/")) {
      safeToast.error("Unsupported file format. Please select an image file.");
      return;
    }
    // const fileSize = e.target.files[0].size / (1024 * 1024);
    // if (fileSize > 2) {
    //   safeToast.error("The file size exceeds 2 MB. Please upload a smaller file.");
    //   return;
    // }
    setSelectedImage(e.target.files[0]);

    if (e.target.files[0]) {
      const reader = new FileReader();
      reader.onload = () => {
        // dispatch({ type: "userProfileEdit", payload: reader.result });
        setPreviewImage(reader.result);
      };
      reader?.readAsDataURL(e.target.files[0]);
    }
  };

  useEffect(() => {
    if (editData) {
      formik.setFieldValue("name", editData?.name);
      formik.setFieldValue(
        "isPrivate",
        editData?.isPrivate ? "Private" : "Public"
      );
      setDefaultVisibility(editData?.isPrivate ? "Private" : "Public");
      if (editData?.image) {
        setPreviewImage(editData?.image);
        setSelectedImage(editData?.image);
      }
      if (editData?.location) {
        setLocation({
          label: editData?.location,
          value: editData?.location,
        });
      }
      if (editData?.description) {
        formik.setFieldValue("description", editData?.description);
      }
      if (editData?.SubProjects) {
        setSubProjectList(
          editData?.SubProjects?.map((ele, i) => ({
            ...ele,
            id: i + 1,
          }))
        );
      }
    }
  }, [editData, editData?.id]);

  // useEffect(() => {
  //   if (formik.values.isPrivate) {
  //     console.log(formik.values.isPrivate, "here iam");
  //   }
  // }, [formik.values.isPrivate]);
  return (
    <>
      {/* Add Sub-Project */}
      <SubProjectModal
        open={isSubProject}
        setOpen={setIsSubProject}
        subProjectList={subProjectList}
        setSubProjectList={setSubProjectList}
        editProjectData={editData}
        modalTitle={"Create sub-project"}
        modalSubmitButton={"Save"}
        isProjectPrivate={formik.values.isPrivate === "Private"}
      />
      {/* Edit Sub-Project */}
      <SubProjectModal
        subProjectList={subProjectList}
        setSubProjectList={setSubProjectList}
        editData={editSubProjectData}
        setEditData={setEditSubProjectData}
        editProjectData={editData}
        modalTitle={"Edit sub-project"}
        modalSubmitButton={"Update"}
        isProjectPrivate={formik.values.isPrivate === "Private"}
      />
      <Modal
        classNames={{
          modal:
            "!tw-max-w-[95vw] !tw-w-full sm:!tw-max-w-[90vw] md:!tw-max-w-[80vw] lg:!tw-max-w-[40rem]  !tw-rounded-[1.25rem] !tw-mt-1 !tw-mx-2 sm:!tw-mx-4 md:!tw-mx-6 lg:!tw-mx-0",
          closeButton: `${isLoading && "!tw-cursor-not-allowed"}`,
          overlay: "!tw-bg-[#000000CC]",
        }}
        center
        styles={{
          modal: {
            position: "relative",
            overflow: "visible",
          },
        }}
        focusTrapped={false}
        open={editData ? editData !== null : open?.isOpen}
        onClose={() => {
          if (!isLoading) {
            resetModal();
          }
        }}
      >
        <div className="tw-py-3 tw-px-4 sm:tw-py-4 sm:tw-px-6 md:tw-py-5 md:tw-px-8 lg:tw-px-12 xl:tw-px-14">
          <h2 className="tw-text-xl md:tw-text-2xl tw-text-center tw-font-semibold ">
            {/* {editData ? "Edit" : "Create"} Project */}
            {modalTitle}
          </h2>
          <p className="tw-text-center tw-text-primary-black tw-font-light tw-text-sm md:tw-text-base tw-mb-4">
            Share the project you are working on.
          </p>
          <div className="tw-h-[60vh] sm:tw-h-[65vh] md:tw-h-[70vh] lg:tw-h-[35rem] tw-overflow-auto tw-pr-2">
            <form onSubmit={formik.handleSubmit} className="tw-mt-4">
              {/* Project Name */}
              <FloatingLabelInput
                label="Project Name*"
                name="name"
                formik={formik}
              />
              {formik.touched.name && formik.errors.name && (
                <p className="tw-text-red-500 tw-text-sm -tw-mt-2 tw-mb-2">
                  {formik.errors.name}
                </p>
              )}
              <div
                onClick={() => {
                  setIsAddMore((prev) => !prev);
                }}
                className="tw-mb-2 tw-cursor-pointer tw-flex tw-justify-between tw-items-center tw-py-2"
              >
                <p className="tw-text-primary-black tw-text-lg">
                  Add More (Optional)
                </p>
                <div className="tw-flex-shrink-0">
                  {!isAddMore ? (
                    <ChevronDown size={20} />
                  ) : (
                    <ChevronUp size={20} />
                  )}
                </div>
              </div>

              <div
                className={`tw-overflow-hidden tw-transition-all tw-duration-300 ${
                  isAddMore
                    ? "tw-max-h-[1500px] tw-opacity-100"
                    : "tw-max-h-0 tw-opacity-0"
                }`}
              >
                {/* Sub Project */}
                {subProjectList?.length > 0 ? (
                  subProjectList?.map((ele, i) => (
                    <div className="" key={`${ele?.name}-${i}`}>
                      <div
                        onClick={(e) => {
                          e.stopPropagation();
                          if (!editData) {
                            setEditSubProjectData(ele);
                          }
                        }}
                        className="tw-mb-4 tw-bg-[#F1F2F3] tw-py-3 sm:tw-py-4 tw-px-4 sm:tw-px-5 tw-rounded-2xl tw-cursor-pointer"
                      >
                        <label className="tw-block tw-text-primary-black tw-text-xs -tw-mt-1 tw-mb-1.5">
                          Sub-project Name {i + 1}
                        </label>
                        <p className="tw-w-full tw-text-primary-black tw-font-semibold tw-text-lg tw-outline-none tw-break-words">
                          {ele?.name}
                        </p>
                      </div>
                      {i !== 0 && !editData && (
                        <div className="tw-flex tw-justify-end">
                          <button
                            type="button"
                            onClick={() => {
                              // setSelectedProject(null);
                              setSubProjectList((prev) =>
                                prev?.filter((data) => data?.id !== ele?.id)
                              );
                            }}
                            className="tw-text-xs -tw-mt-1 tw-mb-2 tw-text-[#EF3B41]"
                          >
                            Remove
                          </button>
                        </div>
                      )}
                    </div>
                  ))
                ) : (
                  <div
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsSubProject(true);
                    }}
                    className="tw-mb-2 tw-bg-[#F1F2F3] tw-py-5 tw-px-5 tw-rounded-2xl tw-cursor-pointer"
                  >
                    <label className="tw-block tw-text-primary-black tw-cursor-pointer tw-text-lg">
                      Sub-Project Name (Optional)
                    </label>
                  </div>
                )}
                <div className="tw-flex tw-justify-end tw-mb-4">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsSubProject(true);
                    }}
                    type="button"
                    className="tw-font-semibold tw-text-primary-purple tw-cursor-pointer tw-text-base tw-py-2 tw-px-1"
                  >
                    + Add More
                  </button>
                </div>
                {/* Description */}
                <FloatingLabelTextArea
                  label="Description"
                  name="description"
                  formik={formik}
                />
                <div className="tw-flex tw-justify-end tw-mb-4">
                  <p className="tw-text-sm tw-text-[#787E89]">
                    {formik.values.description?.length ?? 0}/250
                  </p>
                </div>
                {/* Location */}
                <div className="tw-mb-4">
                  <div className="tw-relative tw-bg-[#F1F2F3] tw-pt-4 sm:tw-pt-5 tw-pb-3 tw-px-4 sm:tw-px-5 tw-rounded-2xl">
                    {/* Floating Label */}
                    <label
                      htmlFor="location"
                      className={`tw-absolute tw-left-4 sm:tw-left-5 tw-text-primary-black tw-transition-all tw-duration-300 tw-pointer-events-none
          ${
            isFocused || location
              ? "tw-top-2 tw-text-xs tw-text-gray-500"
              : "tw-top-5 sm:tw-top-6 tw-text-lg"
          }`}
                    >
                      Location
                    </label>

                    <GooglePlacesAutocomplete
                      apiKey={process.env.NEXT_PUBLIC_GOOGLE_API_KEY}
                      selectProps={{
                        value: location,
                        onChange: (value) => setLocation(value),
                        onFocus: () => setIsFocused(true),
                        onBlur: () => setIsFocused(!!location),
                        placeholder: " ", // keep it blank for floating label trick
                        isClearable: true,
                        name: "location",
                        styles: {
                          control: (base, state) => ({
                            ...base,
                            backgroundColor: "transparent",
                            border: "none",
                            boxShadow: "none",
                            padding: "0",
                            minHeight: "2.5rem",
                            color: "#2D394A",
                            fontSize:
                              window.innerWidth < 640 ? "1rem" : "1.125rem", // Responsive font size
                            fontWeight: 600,
                          }),
                          singleValue: (base) => ({
                            ...base,
                            color: "#2D394A",
                          }),
                          placeholder: (base) => ({
                            ...base,
                            // color: "#BFBFC7",
                          }),
                          indicatorSeparator: () => ({}),
                          dropdownIndicator: () => ({
                            display: "none",
                          }),
                          menu: (base) => ({
                            ...base,
                            zIndex: 100,
                          }),
                        },
                      }}
                    />
                  </div>
                  <p className="tw-italic tw-text-[#787E89] tw-font-light tw-text-sm  tw-mt-2">
                    Add a location to find specific project easily.
                  </p>
                </div>

                {/* Upload Image */}
                {previewImage ? (
                  <div className="tw-relative tw-overflow-visible tw-p-1.5 tw-mb-4">
                    <button
                      onClick={() => {
                        setPreviewImage(null);
                        setSelectedImage(null);
                      }}
                      className="tw-absolute tw-cursor-pointer tw-z-10 -tw-top-1 -tw-right-0"
                    >
                      <CloseModalIcon stroke="black" fill="#d7d4b2" size={20} />
                    </button>
                    <div className="tw-w-full tw-max-w-full tw-overflow-hidden tw-rounded-2xl">
                      <Image
                        src={previewImage}
                        alt="sub-project-image"
                        className="!tw-rounded-2xl tw-w-full tw-h-auto tw-max-h-[200px] sm:tw-max-h-[250px] md:tw-max-h-[300px] tw-object-cover"
                        width={440}
                        height={271}
                      />
                    </div>
                  </div>
                ) : (
                  <div className="tw-relative tw-overflow-hidden tw-mb-4">
                    <div className="tw-cursor-pointer tw-relative tw-overflow-hidden tw-flex tw-justify-center">
                      <div className="tw-scale-75 sm:tw-scale-90 md:tw-scale-100">
                        <ProjectImageUploadIcon />
                      </div>
                      <input
                        type="file"
                        // accept="image/*"
                        accept=".jpg, .jpeg, .png"
                        className="tw-absolute !tw-cursor-pointer tw-inset-0 tw-w-full tw-h-full tw-opacity-0 tw-z-10"
                        onChange={handleProfileImage}
                      />
                    </div>
                  </div>
                )}
              </div>

              <div className="">
                <p className="tw-mt-5 tw-text-lg tw-font-medium tw-text-primary-black">
                  Select Project Visibility*
                </p>
                <p className="tw-text-sm tw-text-[#787E89] tw-mb-2">
                  Public projects are visible to everyone, while private
                  projects are limited to you and your team.
                </p>
                <div className="tw-flex tw-flex-col sm:tw-flex-row tw-gap-2 sm:tw-gap-4 tw-items-start sm:tw-items-center tw-text-lg">
                  {/* Public */}
                  <div className="tw-flex tw-items-center tw-gap-2 tw-py-1">
                    <input
                      type="radio"
                      id="public"
                      name="isPrivate"
                      value="Public"
                      className="tw-cursor-pointer tw-w-4 tw-h-4"
                      checked={formik.values.isPrivate === "Public"}
                      onChange={(e) => {
                        formik.handleChange(e);
                      }}
                    />
                    <label
                      htmlFor="public"
                      className="tw-cursor-pointer tw-text-base"
                    >
                      Public
                    </label>
                  </div>

                  {/* Private */}
                  <div className="tw-flex tw-items-center tw-gap-2 tw-py-1">
                    <input
                      type="radio"
                      id="private"
                      name="isPrivate"
                      value="Private"
                      className="tw-cursor-pointer tw-w-4 tw-h-4"
                      checked={formik.values.isPrivate === "Private"}
                      onChange={(e) => {
                        formik.handleChange(e);
                      }}
                    />
                    <label
                      htmlFor="private"
                      className="tw-cursor-pointer tw-text-base"
                    >
                      Private
                    </label>
                  </div>
                </div>
                {editData && defaultVisibility !== formik.values.isPrivate && (
                  <p className="tw-text-red-500 tw-text-sm tw-mt-2">
                    * Are you sure you want to change the visibility to{" "}
                    {formik.values.isPrivate}? All your content will also be
                    visible to {formik.values.isPrivate}
                  </p>
                )}
              </div>
              <div className="tw-flex tw-justify-center tw-my-4 tw-mt-6">
                <CustomButton
                  loading={isLoading}
                  className={"!tw-px-6 sm:!tw-px-9 !tw-py-3 sm:!tw-py-[14px] "}
                  type="submit"
                  count={8}
                >
                  <span className={"!tw-text-base"}>
                    {/* {editData ? "Update" : "Create"} */}
                    {modalSubmitButton}
                  </span>
                </CustomButton>
              </div>
            </form>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default ProjectModal;
