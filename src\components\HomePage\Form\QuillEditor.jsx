"use client";
import { useQuill } from "react-quilljs";
import "quill/dist/quill.snow.css";
import { useEffect, useRef } from "react";
import { SizeStyle } from "quill/formats/size";
import React from "react";

const QuillEditor = ({
  setValues,
  formik,
  value,
  isUpdate,
  generatedDescription,
}) => {
  const { quill, quillRef, Quill } = useQuill({ theme: "snow" });
  const skipUpdateRef = useRef(false); // 🔒 prevent recursive updates

  useEffect(() => {
    const insertGeneratedDescription = () => {
      if (!quill || !generatedDescription) return;

      const selection = quill.getSelection(true); // focus editor if not focused
      const index = selection ? selection.index : quill.getLength(); // insert at cursor or end

      // Insert as HTML or plain text as needed
      quill.clipboard.dangerouslyPasteHTML(index, generatedDescription);

      // Move cursor to end of inserted text
      const newLength = quill.getLength();
      quill.setSelection(newLength - 1, 0);
    };

    if (quill && generatedDescription) {
      insertGeneratedDescription();
    }
  }, [quill, generatedDescription]);

  useEffect(() => {
    if (Quill && !quill) {
      SizeStyle.whitelist = ["19px", "small", "normal", "large", "huge"];
      Quill.register(SizeStyle, true);
      Quill;
    }
    if (quill) {
      // quill.format("size", "19px");
      quill.format("bold", true);
      if (value) {
        quill.clipboard.dangerouslyPasteHTML(value);
        const length = quill.getLength();
        quill.setSelection(length - 1, 0);
      }

      // Enhanced URL detection function
      // const detectUrls = (text) => {
      //   const urls = [];

      //   // Pattern 1: URLs with protocol (http:// or https://)
      //   const protocolRegex = /(https?:\/\/[^\s]+)/g;
      //   let match;
      //   while ((match = protocolRegex.exec(text)) !== null) {
      //     urls.push({
      //       url: match[0],
      //       index: match.index,
      //       fullUrl: match[0], // Already has protocol
      //       type: "protocol",
      //     });
      //   }

      //   // Pattern 2: URLs starting with www.
      //   const wwwRegex =
      //     /(^|\s)(www\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}(?:\/[^\s]*)?)/g;
      //   wwwRegex.lastIndex = 0;
      //   while ((match = wwwRegex.exec(text)) !== null) {
      //     const url = match[2];
      //     const index = match.index + match[1].length; // Adjust for whitespace

      //     // Check if this overlaps with an existing protocol URL
      //     const overlaps = urls.some(
      //       (existing) =>
      //         index >= existing.index &&
      //         index < existing.index + existing.url.length
      //     );

      //     if (!overlaps) {
      //       urls.push({
      //         url: url,
      //         index: index,
      //         fullUrl: `https://${url}`,
      //         type: "www",
      //       });
      //     }
      //   }

      //   // Allowed TLDs list
      //   const allowedDomain = ["com", "io", "in"];
      //   const tldPattern = allowedDomain.join("|");

      //   // Pattern 3: Domain names without www (only allowed TLDs)
      //   const domainRegex = new RegExp(
      //     `(^|\\s)([\\w-]{1,61}[\\w]\\.(?:${tldPattern})(\\/[^\\s]*)?)`,
      //     "g"
      //   );

      //   while ((match = domainRegex.exec(text)) !== null) {
      //     const url = match[2];
      //     const index = match.index + match[1].length; // Adjust for whitespace

      //     const overlaps = urls.some(
      //       (existing) =>
      //         index >= existing.index &&
      //         index < existing.index + existing.url.length
      //     );
      //     const startsWithWww = url.startsWith("www.");

      //     if (!overlaps && !startsWithWww) {
      //       urls.push({
      //         url: url,
      //         index: index,
      //         fullUrl: `https://${url}`,
      //         type: "domain",
      //       });
      //     }
      //   }

      //   return urls;
      // };

      // let urlDetectionTimeout;

      const handleTextChange = () => {
        if (skipUpdateRef.current) {
          skipUpdateRef.current = false;
          return;
        }

        const html = quill.root.innerHTML;

        formik.setFieldValue("description", html);
        setValues?.((prev) => ({
          ...prev,
          description: html,
        }));

        // // Clear previous timeout
        // if (urlDetectionTimeout) {
        //   clearTimeout(urlDetectionTimeout);
        // }

        // // Delay URL detection to avoid interrupting typing
        // urlDetectionTimeout = setTimeout(() => {
        //   const currentText = quill.getText();
        //   const detectedUrls = detectUrls(currentText);

        //   if (detectedUrls.length > 0) {
        //     // Save current cursor position
        //     const currentSelection = quill.getSelection();

        //     detectedUrls.forEach(({ url, index, fullUrl }) => {
        //       // Skip if this text is already formatted as a link
        //       const format = quill.getFormat(index, url.length);
        //       if (format.link) {
        //         return;
        //       }

        //       // Check if cursor is within or at the end of this URL
        //       const cursorInUrl =
        //         currentSelection &&
        //         currentSelection.index >= index &&
        //         currentSelection.index <= index + url.length;

        //       // Only format if cursor is not actively typing in this URL
        //       if (!cursorInUrl) {
        //         // 🔒 Prevent loop: Skip the next update triggered by formatting
        //         skipUpdateRef.current = true;
        //         quill.formatText(index, url.length, { link: fullUrl });
        //       }
        //     });
        //   }
        // }, 300); // 500ms delay to allow user to finish typing
      };

      const handleKeydown = (e) => {
        if (e.key === "Enter") {
          const selection = quill.getSelection();
          if (selection) {
            const formats = quill.getFormat(selection);
            setTimeout(() => {
              quill.formatLine(selection.index + 1, 1, formats);
              quill.format("bold", true); // ensure bold remains active
            }, 0);
          }
        }
      };

      quill.on("text-change", handleTextChange);
      quill.root.addEventListener("keydown", handleKeydown);

      return () => {
        quill.off("text-change", handleTextChange);
        quill.root.removeEventListener("keydown", handleKeydown);
        // if (urlDetectionTimeout) {
        //   clearTimeout(urlDetectionTimeout);
        // }
      };
    }
  }, [quill]);

  useEffect(() => {
    if (quillRef?.current) {
      const editor = quillRef.current.querySelector(".ql-editor");
      if (editor) {
        editor.setAttribute("spellcheck", "true");
      }
    }
  }, [quillRef]);

  return (
    <div className="tw-h-[32.5rem] tw-mt-5 tw-rounded-lg">
      <div className="add-project-description" ref={quillRef} />
    </div>
  );
};

export default React.memo(QuillEditor);
