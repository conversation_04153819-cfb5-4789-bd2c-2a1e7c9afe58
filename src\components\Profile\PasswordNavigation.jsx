import Modal from "react-responsive-modal";
import { useState } from "react";
import { Check, Eye, EyeOff } from "lucide-react";
import CustomButton from "../Common/Custom-Button";
import toast from "react-hot-toast";
import authStorage from "@/utils/API/AuthStorage";
import { useRouter } from "next/navigation";
import { updatePasswordAPI } from "@/app/action";
import "react-responsive-modal/styles.css";
import { safeToast } from "@/utils/safeToast";
import GlobalForm from "../Common/Custom-Form";
import * as Yup from "yup";
import useApiRequest from "../helper/hook/useApiRequest";

const PasswordNavigation = ({ isOpen, setIsOpen }) => {
  const [oldPassword, setOldPassword] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const api = useApiRequest();

  // Reset State
  const resetModal = () => {
    setIsOpen(false);
    setShowOldPassword(false);
    setShowPassword(false);
    setShowConfirmPassword(false);
    setOldPassword("");
    setPassword("");
    setConfirmPassword("");
    setError("");
  };

  // Password strength validation

  const onLoginSubmit = (values, actions) => {
    let payload = {
      currentPassword: values?.oldPassword,
      newPassword: values?.NewPassword,
    };
    setIsLoading(true);

    api.sendRequest(
      updatePasswordAPI,
      (res) => {
        safeToast.success(res?.message);
        resetModal();
        authStorage.deleteAuthDetails();
        router.push("/login");
        setIsLoading(false);
      },
      payload,
      "",
      (err) => {
        // console.log(error);
        setIsLoading(false);
        safeToast.error(err?.message);
      }
    );
  };

  return (
    <>
      <Modal
        classNames={{
          modal:
            "!tw-max-w-[100%] !tw-w-[37.5rem] !tw-rounded-[1.25rem] !tw-m-0 lg:tw-m-2",
          closeButton: `${isLoading && "!tw-cursor-not-allowed"}`,
          overlay: "!tw-bg-[#000000CC]",
        }}
        center
        focusTrapped={false}
        open={isOpen}
        onClose={() => {
          if (!isLoading) {
            resetModal();
          }
        }} // Fix: Changed `onClose` to `onCancel` for Ant Design modals
      >
        <div className="tw-py-5 tw-mx-10 lg:tw-mx-14">
          <h2 className="tw-text-2xl tw-text-center tw-font-semibold">
            Change Password
          </h2>
          <div className="tw-my-5">
            <GlobalForm
              // loading={api.isLoading}
              loading={isLoading}
              fields={[
                {
                  name: "oldPassword",
                  label: "Old Password",
                  type: "password",
                  required: true,
                },
                {
                  name: "password",
                  label: "New Password",
                  type: "passwordWithVal",
                  required: true,
                },
                {
                  name: "NewPassword",
                  label: "Confirm New Password",
                  type: "password",
                  required: true,
                },
              ]}
              initialValues={{ password: "", NewPassword: "" }}
              validationSchema={{
                oldPassword: Yup.string().trim().required(""),
                password: Yup.string()
                  .trim()
                  .required("Password is required")
                  .min(8, "Password must be at least 8 characters long")
                  .matches(
                    /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&._-])[^\s]*$/,
                    "(A-Z, a-z, 0-9, @$!%*?&._-), at least 1 of each & no spaces."
                  ),
                NewPassword: Yup.string()
                  .trim()
                  .required("All fields are required.")
                  .test(
                    "not-same-as-password",
                    "New password can not be same as old one.",
                    function (value) {
                      const { oldPassword } = this.parent;
                      return value !== oldPassword;
                    }
                  )
                  .oneOf([Yup.ref("password")], "Passwords do not match."),
              }}
              onSubmit={(e, actions) => onLoginSubmit(e, actions)}
              submitButtonText="Update"
            />
          </div>
        </div>
      </Modal>
    </>
  );
};

export default PasswordNavigation;
