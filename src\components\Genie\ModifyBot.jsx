import { handleCopy, ReplaceBackslashN } from "@/utils/function";
import { GenieChatIcon, SentChatIcon } from "@/utils/icons";
import dayjs from "dayjs";
import { Copy, X } from "lucide-react";
import { useRef } from "react";

const ModifyBot = ({
  message,
  setMessage,
  firstTimeLoading,
  chatList = [],
  setIsBotOpen,
  isMessageFetching,
  chatHandler,
  isDataFetching,
  chatContainerRef,
  textareaRef,
  role,
  userChatCount,
}) => {
  return (
    <>
      <div className="tw-relative tw-h-full">
        <div
          ref={chatContainerRef}
          className="tw-h-[40rem] lg:tw-h-[77vh] xxl:tw-h-[82vh] tw-overflow-auto show-scrollbar tw-pr-2"
        >
          {firstTimeLoading ? (
            <div className="tw-my-2 tw-flex tw-gap-2 tw-items-center">
              <GenieChatIcon size={25} /> <MessageLoader />
            </div>
          ) : (
            <div className="tw-pb-2">
              {chatList?.map((ele, i) => (
                <div
                  key={`${ele.content}-${i}`}
                  className={`tw-flex  tw-w-full ${
                    ele?.role === role?.user
                      ? "tw-justify-end"
                      : "tw-items-start tw-gap-2"
                  }`}
                >
                  {ele?.role === role?.assistant && (
                    <div className="tw-relative tw-top-2">
                      <GenieChatIcon size={25} />
                    </div>
                  )}
                  <div className="tw-flex tw-flex-col tw-max-w-[85%]">
                    <div
                      className={`  ${
                        ele?.role === role?.user
                          ? " tw-rounded-bl-2xl tw-bg-[#F6EFFE]"
                          : "tw-rounded-br-2xl tw-bg-[#F7F7F2]"
                      } tw-my-2  tw-block tw-rounded-tl-2xl tw-rounded-tr-2xl  tw-p-2.5 ${
                        i % 10 !== 0 && "tw-break-all"
                      } `}
                    >
                      {i % 10 === 0 ? (
                        ele?.content
                      ) : (
                        <div
                          className="tw-text-primary-black tw-font-semibold"
                          dangerouslySetInnerHTML={{
                            __html: ReplaceBackslashN(ele?.content),
                          }}
                        />
                      )}
                      <p className="tw-text-xs tw-text-right tw-text-[#67737D] tw-font-light">
                        {dayjs(ele.timestamp).format("hh:mm A")}
                      </p>
                    </div>
                    <button
                      onClick={() => {
                        handleCopy(ele?.content);
                      }}
                      type="button"
                    >
                      <Copy size={15} />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
          {isMessageFetching && (
            <div className="tw-my-2 tw-flex tw-gap-2 tw-items-center">
              <GenieChatIcon size={25} /> <MessageLoader />
            </div>
          )}
        </div>

        {/* Input Message */}
        <div className="tw-absolute tw-bottom-0 tw-w-full">
          <div className="tw-flex tw-gap-2 tw-items-center">
            <textarea
              ref={textareaRef}
              type="text"
              name="content"
              value={message ?? ""}
              onFocus={(e) => {
                e.target.scrollIntoView({
                  behavior: "smooth",
                  block: "center",
                });
              }}
              onInput={() => {
                if (!isDataFetching) {
                  const textarea = textareaRef.current;
                  if (textarea) {
                    textarea.style.height = "auto"; // reset to auto to shrink if needed
                    textarea.style.height = `${textarea.scrollHeight}px`; // set height to scrollHeight
                  }
                }
              }}
              onKeyDown={(event) => {
                if (event.key === "Enter" && !isDataFetching && message) {
                  event.preventDefault();
                  chatHandler();
                }
              }}
              onChange={(e) => {
                setMessage(e.target.value?.replace(/\n/g, ""));
              }}
              rows={1}
              placeholder="Message"
              className="tw-rounded-lg tw-font-normal tw-py-[0.75rem] tw-outline-none tw-ps-3 tw-pe-2 tw-w-full tw-bg-[#F1F2F3] tw-text-primary-black tw-resize-none tw-overflow-hidden tw-min-h-[2rem]"
            />

            {message && !isDataFetching && (
              <button
                onClick={chatHandler}
                type="submit"
                className="tw-cursor-pointer"
              >
                <SentChatIcon size={35} />
              </button>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default ModifyBot;

function MessageLoader() {
  return (
    <div className="tw-bg-[#F7F7F2] tw-inline-block tw-rounded-tl-2xl tw-rounded-tr-2xl tw-rounded-br-2xl tw-p-2.5">
      <div className="chat-loader"></div>
    </div>
  );
}
