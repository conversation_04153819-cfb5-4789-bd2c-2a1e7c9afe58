import { useCallback, useContext, useEffect, useRef, useState } from "react";
import Image from "next/image";
import logo from "../../../../public/images/logo/logo-primary.svg";
import QuillEditor from "./QuillEditor";
import { useFormik } from "formik";
import * as Yup from "yup";
import CustomButton from "@/components/Common/Custom-Button";
import Modal from "react-responsive-modal";
import {
  CheckMarkIcon,
  GenieChatIcon,
  LeftArrowBackIcon,
  UploadImageIcon,
} from "@/utils/icons";
import { ChevronRight, CircleX } from "lucide-react";
import toast from "react-hot-toast";
import {
  base64ToFile,
  blurDataURL,
  convertImage,
  getImageHeight,
  getMaxImageHeight,
  isBase64Image,
  isObjEmpty,
  removeBackgroundColor,
  ReplaceBackslashN,
  replaceBase64WithLinks,
  RESPONSE_STATUS,
  routes,
} from "@/utils/function";
import SelectProjectModal from "./SelectProjectModal";
import { addPost, editPost, updateImageToURL } from "@/app/action";
import { valContext } from "@/components/context/ValContext";
import FullScreenLoader from "@/components/Loader/FullScreenLoader";
import "react-responsive-modal/styles.css";
import useApiRequest from "@/components/helper/hook/useApiRequest";
import { safeToast } from "@/utils/safeToast";
import tempProjectImg from "../../../../public/images/assets/default_image.png";
import { useRouter } from "next/navigation";

import { io } from "socket.io-client";

const PostModal = ({
  open,
  setOpen,
  setRefresh = () => {},
  reFetchData = () => {},
  editData = null,
  setEditData,
  showSaveAsDraft = true,
  showPostVisibility = true,
  showProject = true,
  CommunityId = null,
  title = null,
  formData = null,
  onlyForPost = false,
  duplicateData,
  isRemoveAble = true,
  postForProject = false,
  postForSubProject = false,
}) => {
  const [isFocus, setIsFocus] = useState(false);
  const [imageSelection, setImageSelection] = useState(false);
  const [imagePreviewList, setImagePreviewList] = useState([]);
  const [selectedImage, setSelectedImage] = useState(null);
  const [values, setValues] = useState({
    title: null,
    description: null,
  });
  const [isProjectModalOpen, setIsProjectModalOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState(null);
  const [selectedSubProject, setSelectedSubProject] = useState(null);
  const [selectedValue, setSelectedValue] = useState("Public"); // Default value
  const [isLoading, setIsLoading] = useState(false);
  const [draftLoader, setDraftLoader] = useState(false);
  const [isBotOpen, setIsBotOpen] = useState(false);
  const api = useApiRequest();
  const { setLatestCreatedPost, setSubProjectPostCount } =
    useContext(valContext);
  const [message, setMessage] = useState("");
  const [isDataFetching, setIsDataFetching] = useState(false);
  const [isDataFetched, setIsDataFetched] = useState(false);
  const [socket, setSocket] = useState(null);
  const [generatedDescription, setGeneratedDescription] = useState("");
  const [isDescriptionUpdate, setIsDescriptionUpdate] = useState("");
  const router = useRouter();

  const targetRef = useRef(null);
  const textareaRef = useRef(null);

  // console.log(selectedProject);

  // console.log(imagePreviewList);

  const resetModal = () => {
    if (editData) {
      setEditData(null);
    } else {
      setOpen((prev) => ({
        ...prev,
        isOpen: false,
      }));
    }
    setDraftLoader(false);
    setIsLoading(false);
    setIsFocus(false);
    setValues({
      title: null,
      description: null,
    });
    setSelectedProject(null);
    setImageSelection(false);
    setImagePreviewList([]);
    setSelectedImage(null);
    formik.resetForm();
    // setSocket(null);
    setGeneratedDescription("");
    setIsDataFetched(false);
  };
  function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
  }

  function isImageUrlInDescription(description, imageUrl) {
    if (!description || !imageUrl) return false;

    const escapedUrl = escapeRegExp(imageUrl);
    const regex = new RegExp(`<img[^>]*src=["']${escapedUrl}["'][^>]*>`, "i");
    return regex.test(description);
  }

  // Submit Handler
  const formSubmitHandler = async (isPublished = true, data) => {
    if (isPublished) {
      setIsLoading(true);
    } else {
      setDraftLoader(true);
    }
    try {
      let payload = {
        title: values.title?.toString() ?? data?.title?.toString() ?? "",
        description: values?.description ?? data?.description?.toString() ?? "",
        isPublished,
        isPrivate: selectedValue === "Private",
      };

      const maxHeight = await getMaxImageHeight(imagePreviewList);

      // console.log("Max height is ------>", maxHeight);

      if (maxHeight) {
        payload = {
          ...payload,
          properties: JSON.stringify({
            height: maxHeight <= 520 ? maxHeight : 520,
          }),
        };
      }

      // console.log(payload);

      if (selectedSubProject || data?.subProjectId) {
        payload.ProjectId = selectedSubProject?.id || data?.subProjectId;
      } else if (selectedProject || data?.projectId) {
        payload.ProjectId = selectedProject?.id || data?.projectId;
      }

      if (CommunityId && !editData) {
        payload = {
          ...payload,
          CommunityId,
        };
      }

      // Convert images to API-ready format (Properly handling async requests)
      const media = await Promise.all(
        imagePreviewList.map(async (ele, i) => {
          if (!isBase64Image(ele?.src)) {
            let data = {
              ...ele,
              link: ele?.src ?? ele?.link,
            };
            if (selectedImage?.id === ele?.id) {
              data.isCoverImage = true;
            }
            return data;
          }

          const imgSrc = base64ToFile(ele?.src, `${i + 1}.png`);
          const formData = new FormData();
          formData.append("file", imgSrc);

          try {
            const res = await updateImageToURL(formData);
            const imageHeight = await getImageHeight(ele?.src);
            if (
              res?.status === 200 ||
              res?.status === RESPONSE_STATUS.SUCCESS
            ) {
              if (!res?.data?.[0]?.link) {
                throw new Error("Image upload failed");
              }
              let data = {
                link: res?.data[0]?.link,
                type: res?.data[0]?.type,
                src: ele?.src,
                showingCoverImage: ele?.showingCoverImage,
                height: imageHeight,
              };

              if (selectedImage?.id === ele?.id) {
                data.isCoverImage = true;
              }

              return data;
            } else {
              throw res;
            }
          } catch (error) {
            console.error("Image upload failed:", error);
            setIsLoading(false);
            return;
          }
        })
      );

      const description = payload?.description
        ? removeBackgroundColor(
            replaceBase64WithLinks(payload?.description, media)
          )
        : "";
      // console.log({
      //   ...payload,
      //   description: !description || description === "" ? null : description,
      // });

      if (media.length > 0) {
        payload = {
          ...payload,
          media: media
            ?.filter((ele) => ele?.showingCoverImage)
            ?.map((ele) => {
              let data = {
                link: ele?.link,
                type: ele?.type,
                height: ele?.height,
              };
              if (ele?.isCoverImage) {
                data = {
                  ...data,
                  isCoverImage: ele?.isCoverImage,
                  isCustom: !isImageUrlInDescription(description, ele?.link),
                };
              }

              return data;
            }),
        };
      }

      // Creating the Post first time
      let POST_API;
      if (editData) {
        POST_API = editPost;
        payload = {
          ...payload,
          id: editData?.id,
        };
      } else {
        POST_API = addPost;
      }
      api.sendRequest(
        POST_API,
        (res) => {
          // console.log(res, "res");
          if (!editData && isPublished && !title && onlyForPost) {
            setLatestCreatedPost(res?.data);
          }
          if (isPublished) {
            router.push(
              routes.SINGLE_POST?.replace(
                ":slug",
                res?.data?.slug ?? editData?.slug
              )
            );
          }
          if (editData) {
            setLatestCreatedPost(null);
          }
          // Increase SubProject Post Count
          if (postForSubProject) {
            setSubProjectPostCount((prev) => +prev + 1);
          }

          if (!isPublished) {
            safeToast.success("Post saved to draft successfully.", {
              duration: 3000,
              style: {
                background: "#ff8f00",
                color: "#fff",
              },
              iconTheme: {
                primary: "#ffa733",
              },
            });
          } else {
            safeToast.success(res?.message);
          }
          reFetchData();
          // setRefresh((prev) => !prev);
          setRefresh((prev) => (typeof prev === "undefined" ? true : !prev));
          resetModal();
        },
        {
          ...payload,
          description: !description || description === "" ? null : description,
        },
        ``,
        () => {
          setIsLoading(false);
          setDraftLoader(false);
        }
      );

      // You can now send the payload with images
    } catch (error) {
      console.error("Form submission error:", error);
      safeToast.error("Something went wrong while submitting the form");
    }
  };

  // Image Preview Handler
  const handleProfileImage = (e) => {
    if (!e.target.files[0] || !e.target.files[0].type.startsWith("image/")) {
      safeToast.error("Unsupported file format. Please select an image file.");
      return;
    }
    // const fileSize = e.target.files[0].size / (1024 * 1024);
    // if (fileSize > 5) {
    //   safeToast.error("The file size exceeds 5 MB. Please upload a smaller file.");
    //   return;
    // }
    // setImage(e.target.files[0]);

    if (e.target.files[0]) {
      const reader = new FileReader();
      reader.onload = () => {
        // dispatch({ type: "userProfileEdit", payload: reader.result });
        setImagePreviewList((prev) =>
          prev.length === 0
            ? [
                {
                  id: 1,
                  src: reader.result,
                  onlyCoverImage: true,
                  showingCoverImage: true,
                },
              ]
            : [
                {
                  id: prev.length + 1,
                  src: reader.result,
                  onlyCoverImage: true,
                  showingCoverImage: true,
                },
                ...prev,
              ]
        );
        setSelectedImage({
          id: imagePreviewList?.length + 1,
          src: reader.result,
          onlyCoverImage: true,
          showingCoverImage: true,
        });
      };
      reader?.readAsDataURL(e.target.files[0]);
    }
  };

  const validationSchema = Yup.object().shape({
    title: Yup.string().trim().required("Title is required"),

    // description: Yup.string().test(
    //   "required",
    //   "Description is required",
    //   (value) => {
    //     // if (CommunityId) return true; // Skip validation if communityId is present

    //     // if (isDescriptionRequired) {
    //     //   const html = value || "";
    //     //   const text = stripHtml(html).trim();
    //     //   const hasImage = /<img\s+[^>]*src=["'][^"']+["'][^>]*>/i.test(html);
    //     //   return text.length > 0 || hasImage;
    //     // }
    //     return true;
    //   }
    // ),
  });

  // console.log(imagePreviewList, "Preview List");

  const formik = useFormik({
    initialValues: {
      title: editData ? editData?.title : "",
      description: editData ? editData?.description ?? "" : "",
    },

    validationSchema,
    onSubmit: (value) => {
      // console.log("Form Submitted:", value);
      if (Object.keys(value).length > 0) {
        setImageSelection((prev) => !prev);
        setValues({
          title: value?.title?.trim(),
          description: value?.description?.trim(),
        });
        const imageList = convertImage(value?.description)
          ?.filter((ele) => isBase64Image(ele))
          ?.map((ele, i) => ({
            id: i + 30, // add the 30 here because if the image is link the the uploaded image start with the same id so just uniquely identify add the 30
            src: ele,
            showingCoverImage: true,
          }));

        // console.log("Next Image List", imageList);

        // if (editData) {
        //   setImagePreviewList((prev) => [...prev, ...imageList]);
        // } else {
        //   setImagePreviewList(imageList);
        // }
        if (editData || duplicateData) {
          setImagePreviewList((prev) => {
            const existingSrcSet = new Set(prev.map((img) => img.src));
            const uniqueNewImages = imageList.filter(
              (img) => !existingSrcSet.has(img.src)
            );
            return [...prev, ...uniqueNewImages];
          });
        } else {
          // setImagePreviewList((prev) => [...prev, ...imageList]);
          setImagePreviewList((prev) => {
            const existingIds = new Set(prev.map((item) => item.id));

            const newImages = imageList.filter(
              (item) => !existingIds.has(item.id)
            );

            return [...prev, ...newImages];
          });
        }

        if (!duplicateData && !editData && imageList?.length > 0) {
          setSelectedImage((prev) =>
            prev
              ? prev
              : {
                  id: imageList[imageList?.length - 1]?.id,
                  src:
                    imageList[imageList?.length - 1]?.link ??
                    imageList[imageList?.length - 1]?.src,
                  showingCoverImage:
                    imageList[imageList?.length - 1]?.showingCoverImage,
                }
          );
        }
      }
    },
  });

  const formikRef = useRef();

  // console.log(formik.values);

  const handleModalClose = () => {
    const currentFormik = formikRef.current;
    // console.log(currentFormik, "selectedProject?.id");
    // console.log("Current formik values:", currentFormik.values);
    const data = {
      title: currentFormik?.formik?.values?.title,
      description: currentFormik?.formik?.values?.description,
      projectId: currentFormik?.selectedProject?.id ?? null,
      subProjectId: currentFormik?.selectedSubProject?.id ?? null,
    };
    if (currentFormik?.formik?.values.title && !editData && !CommunityId) {
      formSubmitHandler(false, {
        ...data,
      });
    } else if (editData && !editData?.isPublished) {
      formSubmitHandler(false, {
        ...data,
      });
    }
    if (!isLoading) {
      resetModal();
    }
  };

  const autoResize = (textarea) => {
    textarea.style.height = "auto";
    textarea.style.height = textarea.scrollHeight + "px";
  };

  const chatHandler = () => {
    if (!message) return;
    setIsDataFetching(true);
    const payload = [
      {
        role: "user",
        content: message,
      },
    ];
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = "auto"; // reset height immediately after send
    }
    setMessage("");
    socket.emit("sendMessage", {
      conversations: payload,
    });
  };

  useEffect(() => {
    formikRef.current = {
      formik: formik,
      selectedProject: selectedProject,
      selectedSubProject: selectedSubProject,
    };
  }, [formik, selectedProject, selectedSubProject]);
  // console.log(formik);
  useEffect(() => {
    if (editData) {
      formik.setFieldValue("title", editData?.title);
      formik.setFieldValue("description", editData?.description);
      setValues({
        title: editData?.title,
        description: editData?.description,
      });

      setSelectedValue(editData?.isPrivate ? "Private" : "Public");

      // setSelectedProject(editData?.Project);
      if (editData?.Project?.ParentProject) {
        setSelectedProject(editData?.Project?.ParentProject);
        setSelectedSubProject(editData?.Project);
      } else if (editData?.Project) {
        setSelectedProject(editData?.Project);
      }
      if (editData?.media?.length > 0) {
        const mediaList = editData?.media?.map((ele, i) => ({
          ...ele,
          showingCoverImage: true,
          id: i + 1,
        }));
        setImagePreviewList(
          mediaList?.map((ele) => ({
            id: ele?.id,
            link: ele?.link,
            type: ele?.type,
            showingCoverImage: ele?.showingCoverImage,
          }))
        );
        setSelectedImage(mediaList?.find((ele) => ele?.isCoverImage));
      }

      if (editData?.description) {
        setIsFocus(true);
      }
    }
  }, [editData]);

  useEffect(() => {
    if (duplicateData) {
      formik.setFieldValue("title", duplicateData?.title);
      formik.setFieldValue("description", duplicateData?.description ?? "");
      setValues({
        title: duplicateData?.title,
        description: duplicateData?.description ?? "",
      });

      setSelectedValue(duplicateData?.isPrivate ? "Private" : "Public");

      if (duplicateData?.ParentProject) {
        setSelectedProject(duplicateData?.ParentProject);
        setSelectedSubProject(duplicateData);
      } else if (duplicateData?.Project) {
        setSelectedProject(duplicateData?.Project);
      }

      if (duplicateData?.media?.length > 0) {
        const mediaList = duplicateData?.media?.map((ele, i) => ({
          ...ele,
          showingCoverImage: true,
          id: i + 1,
        }));
        setImagePreviewList(
          mediaList?.map((ele) => ({
            id: ele?.id,
            link: ele?.link,
            type: ele?.type,
            showingCoverImage: ele?.showingCoverImage,
          }))
        );
        setSelectedImage(mediaList?.find((ele) => ele?.isCoverImage));
      }

      if (duplicateData?.description) {
        setIsFocus(true);
      }
    }
  }, [duplicateData]);
  useEffect(() => {
    if (formData) {
      // setSelectedProject(formData);
      if (formData && !isObjEmpty(formData)) {
        if (formData?.ParentProject) {
          setSelectedProject(formData?.ParentProject);
          setSelectedSubProject(formData);
        } else {
          setSelectedProject(formData);
        }
      }
    }
  }, [formData]);

  // Socket Connection
  useEffect(() => {
    const newSocket = io(process.env.NEXT_PUBLIC_SOCKET_URL, {
      autoConnect: false,
    });

    newSocket.connect();

    newSocket.on("connect", () => {
      // console.log("✅ Socket connected");
    });

    let chunkTimeout;
    newSocket.on("chatChunk", (data) => {
      // console.log(data, "data");

      if (Object.keys(data).length > 0) {
        // setIsDataFetching(false);

        // Clear existing timeout
        if (chunkTimeout) clearTimeout(chunkTimeout);

        // Process the chunk data here
        // Update your chat/message state
        setGeneratedDescription((prev) => prev + data?.content);
        targetRef.current.scrollIntoView({
          behavior: "smooth",
          block: "end",
        });

        // Set new timeout for completion detection
        chunkTimeout = setTimeout(() => {
          setIsDataFetching(false);
          setIsDataFetched(true);
        }, 500);
      } else {
        // setIsDataFetching(false);
      }
    });

    newSocket.on("disconnect", () => {
      // console.log("❌ Socket disconnected");
    });

    newSocket.on("connect_error", (err) => {
      // console.error("⚠️ Socket connect error:", err.message);
      setIsDataFetching(false); // Reset loading state on error
    });

    setSocket(newSocket);

    return () => {
      if (chunkTimeout) clearTimeout(chunkTimeout);
      if (newSocket) {
        newSocket.removeAllListeners(); // Remove ALL listeners
        newSocket.disconnect();
      }
    };
  }, []);
  return (
    <Modal
      classNames={{
        modal: "!tw-max-w-[100%] !tw-w-[200rem] !tw-m-0 !tw-h-full ",
        closeButton: `${isLoading && "!tw-cursor-not-allowed"}`,
        overlay: "!tw-bg-[#000000CC]",
      }}
      focusTrapped={false}
      open={editData ? editData !== null : open?.isOpen}
      // onEscKeyDown={handleModalClose}
      // onEscKeyDown={() => {
      //   console.log("here");
      // }}
      onClose={handleModalClose}
    >
      {draftLoader && <FullScreenLoader />}
      {/* Select Project Modal */}
      {showProject && (
        <SelectProjectModal
          open={isProjectModalOpen}
          setIsOpen={setIsProjectModalOpen}
          selectedProject={selectedProject}
          setSelectedProject={setSelectedProject}
          selectedSubProject={selectedSubProject}
          setSelectedSubProject={setSelectedSubProject}
          postForProject={postForProject || editData}
          postForSubProject={postForSubProject}
        />
      )}

      <Modal
        classNames={{
          modal:
            " !tw-m-0 !tw-max-[100%] !tw-h-[36rem] !tw-w-[100%] lg:!tw-w-[50rem] !tw-m-0 lg:!tw-m-2 !tw-rounded-[1.25rem]",
          // closeButton: `${isLoading && "!tw-cursor-not-allowed"}`,
          overlay: "!tw-bg-[#000000CC]",
        }}
        focusTrapped={false}
        open={isBotOpen}
        center
        // onEscKeyDown={handleModalClose}
        onClose={() => {
          setIsBotOpen(false);
          setMessage("");
        }}
      >
        <div className="tw-relative tw-h-full">
          <h1 className="tw-text-base md:tw-text-xl tw-font-medium tw-flex tw-gap-2 tw-items-center tw-mb-2">
            <GenieChatIcon bgFill="#00000000" />
            <p>Ask Genie</p>
          </h1>
          <div className="tw-h-[27rem] tw-overflow-auto">
            <p
              ref={targetRef}
              className="tw-text-primary-black"
              dangerouslySetInnerHTML={{
                __html: ReplaceBackslashN(generatedDescription),
              }}
            />
          </div>
          <div className="tw-absolute tw-bottom-1 tw-w-full tw-right-0 tw-left-0">
            {isDataFetched ? (
              <div className="tw-flex tw-justify-center ">
                <CustomButton
                  className={
                    "!tw-px-7 !tw-py-[.85rem] !tw-flex !tw-justify-center !tw-items-center"
                  }
                  onClick={() => {
                    // values.description
                    setIsBotOpen(false);
                    formik.setFieldValue(
                      "description",
                      ReplaceBackslashN(generatedDescription)
                    );
                    setIsDescriptionUpdate(
                      ReplaceBackslashN(generatedDescription)
                    );
                    // setValues((prev) => ({
                    //   ...prev,
                    //   description: ReplaceBackslashN(generatedDescription),
                    // }));
                    setIsFocus(true);
                  }}
                  count={8}
                >
                  Insert
                </CustomButton>
              </div>
            ) : (
              <div
                className={`tw-flex tw-gap-2 tw-items-center ${
                  isDataFetching && "tw-justify-center"
                }`}
              >
                {!isDataFetching && (
                  <textarea
                    type="text"
                    ref={textareaRef}
                    name="content"
                    value={message ?? ""}
                    onFocus={(e) => {
                      e.target.scrollIntoView({
                        behavior: "smooth",
                        block: "center",
                      });
                    }}
                    onInput={() => {
                      if (!isDataFetching) {
                        const textarea = textareaRef.current;
                        if (textarea) {
                          textarea.style.height = "auto"; // reset to auto to shrink if needed
                          textarea.style.height = `${textarea.scrollHeight}px`; // set height to scrollHeight
                        }
                      }
                    }}
                    onKeyDown={(event) => {
                      if (event.key === "Enter" && !isDataFetching && message) {
                        event.preventDefault();
                        chatHandler();
                      }
                    }}
                    onChange={(e) => {
                      setMessage(e.target.value?.replace(/\n/g, ""));
                    }}
                    rows={1}
                    placeholder="Type message"
                    className="tw-rounded-lg tw-font-normal !tw-py-[.85rem] tw-outline-none tw-ps-3 tw-pe-2 tw-w-full tw-bg-[#F1F2F3] tw-text-primary-black tw-resize-none tw-overflow-hidden tw-min-h-[2rem]"
                  />
                )}
                <CustomButton
                  onClick={chatHandler}
                  disabled={isDataFetching || !message}
                  className={`!tw-px-7 !tw-py-[.85rem] !tw-flex !tw-justify-center !tw-items-center ${
                    (isDataFetching || !message) && "tw-bg-opacity-75"
                  }`}
                  count={8}
                >
                  {isDataFetching ? "Creating..." : "Create"}
                </CustomButton>
              </div>
            )}
          </div>
        </div>
      </Modal>

      {/* Post Form */}
      <div className="tw-h-full">
        <div className="tw-relative tw-w-[7.5rem] tw-h-[2.8rem] ">
          <Image src={logo} alt="logo" fill className="" />
        </div>
        <div className="tw-max-w-[65rem] tw-mx-auto tw-relative tw-pt-8 lg:tw-p-0">
          <div>
            {imageSelection && (
              <button
                onClick={() => {
                  setImageSelection(false);
                }}
                className="tw-mb-4 tw-block"
              >
                <LeftArrowBackIcon />
              </button>
            )}
            <p
              className={`tw-font-bold tw-text-3xl ${
                !imageSelection ? "tw-mb-10" : "tw-mt-0 tw-mb-3"
              }`}
            >
              {imageSelection ? (title ? title : "Publish") : ""}
            </p>
          </div>
          {/* Post Form */}
          {!imageSelection && (
            <form className="" onSubmit={formik.handleSubmit}>
              {/* Title */}
              <div className="tw-w-full lg:tw-w-[66%]">
                {/* // Then use it in your JSX */}
                <textarea
                  name="title"
                  value={formik.values.title}
                  onChange={(e) => {
                    formik.handleChange(e);
                    autoResize(e.target);
                  }}
                  onBlur={formik.handleBlur}
                  className="post-form tw-text-3xl tw-w-full tw-font-semibold tw-outline-none tw-border-none tw-resize-none tw-overflow-hidden"
                  placeholder="Write your post"
                  maxLength={200}
                  rows={1}
                  style={{ minHeight: "30px" }}
                />
                {formik.touched.title && formik.errors.title && (
                  <p className="tw-text-red-500 tw-text-sm">
                    {formik.errors.title}
                  </p>
                )}
              </div>
              {/* Description */}
              <input
                type="text"
                className={`post-form tw-mt-3 tw-font-medium tw-outline-none tw-border-none ${
                  isFocus && "tw-hidden"
                }`}
                placeholder="Description"
                onFocus={() => setIsFocus(true)}
                onBlur={(e) => {
                  formik.handleBlur(e);
                  setIsFocus(!values.description);
                }}
              />
              <div>
                {isFocus && (
                  <div className="tw-pb-16 ">
                    <QuillEditor
                      formik={formik}
                      isFocus={isFocus}
                      setValues={setValues}
                      value={values.description}
                      // isUpdate={isDescriptionUpdate}
                      generatedDescription={isDescriptionUpdate}
                    />
                  </div>
                )}
                {formik.touched.description && formik.errors.description && (
                  <p className="tw-text-red-500 tw-text-sm">
                    {formik.errors.description}
                  </p>
                )}
              </div>

              {/* Submit Button */}
              <div className="tw-absolute -tw-top-2 lg:-tw-top-2 tw-right-0 tw-flex tw-gap-2 tw-items-center">
                <CustomButton
                  className={"!tw-px-9 !tw-py-[14px]"}
                  type="submit"
                  count={8}
                >
                  <span className={"!tw-text-base"}>Next</span>
                </CustomButton>
                {!imageSelection && (
                  <button
                    className="tw-flex tw-gap-1 tw-items-center tw-justify-center tw-text-primary-black tw-font-medium tw-border tw-border-primary-black tw-rounded-full tw-py-2 tw-px-5 tw-text-lg"
                    type="button"
                    onClick={() => {
                      setIsDataFetched(false);
                      setIsDataFetching(false);
                      setGeneratedDescription("");
                      setIsBotOpen(true);
                    }}
                  >
                    <GenieChatIcon bgFill="#00000000" />
                    <p className="tw-hidden lg:tw-block">Ask Genie</p>
                  </button>
                )}
              </div>
            </form>
          )}
          {/* Cover Image Selection */}
          {imageSelection && (
            <div>
              <p className="tw-text-primary-black">
                Select a cover photo from the post images.
              </p>
              {/* Image Selections */}
              <div className="tw-flex tw-gap-5 tw-items-center tw-flex-wrap">
                {imagePreviewList?.filter((ele) => ele?.onlyCoverImage)
                  ?.length === 0 && (
                  <div className="tw-my-5 tw-relative tw-overflow-hidden tw-inline-block">
                    <UploadImageIcon width={80} height={65} />
                    <input
                      type="file"
                      accept="image/*"
                      className="tw-absolute tw-top-[1.35rem] tw-cursor-pointer tw-opacity-0 tw-scale-[2]"
                      onChange={(e) => {
                        handleProfileImage(e);
                      }}
                    />
                  </div>
                )}
                <div className="tw-flex tw-my-5 tw-gap-5 tw-items-center tw-flex-wrap ">
                  {imagePreviewList
                    .sort((a, b) => b?.id - a?.id)
                    ?.map(
                      (ele) =>
                        ele?.showingCoverImage && (
                          <div
                            key={ele?.id}
                            className={`tw-cursor-pointer tw-relative tw-rounded-xl`}
                            onClick={() => {
                              setSelectedImage(ele);
                            }}
                          >
                            {selectedImage?.id === ele?.id && (
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  if (
                                    selectedImage?.onlyCoverImage &&
                                    selectedImage?.showingCoverImage
                                  ) {
                                    setImagePreviewList((prev) =>
                                      prev?.filter(
                                        (ele) => ele?.id !== selectedImage?.id
                                      )
                                    );
                                  } else {
                                    setImagePreviewList((prev) => [
                                      ...prev?.filter(
                                        (ele) => ele?.id !== selectedImage?.id
                                      ),
                                      ...prev
                                        ?.filter(
                                          (ele) => ele?.id === selectedImage?.id
                                        )
                                        ?.map((ele) => ({
                                          ...ele,
                                          showingCoverImage: false,
                                        })),
                                    ]);
                                  }
                                  setSelectedImage(null);
                                }}
                                className="tw-absolute -tw-top-4 -tw-right-3"
                              >
                                <CircleX
                                  fill="#EF3B41"
                                  strokeWidth={1.5}
                                  stroke="#fff"
                                />
                              </button>
                            )}
                            {selectedImage?.id === ele?.id && (
                              <div className="tw-absolute tw-right-2 tw-top-2">
                                <CheckMarkIcon size={22} />
                              </div>
                            )}

                            <Image
                              src={ele?.link || ele?.src}
                              alt="cover-image"
                              className={`!tw-h-[65px] tw-object-contain tw-rounded-xl tw-border-[2.76px] ${
                                selectedImage?.id === ele?.id
                                  ? " tw-border-[#10BE5B]"
                                  : "tw-border-transparent"
                              }`}
                              width={80}
                              height={85}
                            />
                          </div>
                        )
                    )}
                </div>
              </div>
              {/* Select Project */}
              {showProject && (
                <button
                  onClick={() => {
                    if (!formData) {
                      setIsProjectModalOpen((prev) => !prev);
                    }
                  }}
                  className={`tw-flex tw-gap-2 tw-items-center ${
                    formData && "tw-cursor-default"
                  }`}
                >
                  <p className="tw-my-5 tw-text-lg tw-font-medium tw-text-primary-black ">
                    Select Project (optional)
                  </p>
                  <ChevronRight stroke="#2D394A" size={22} />
                </button>
              )}
              {/* Project Details */}
              {selectedProject && (
                <div
                  onClick={() => {
                    setIsProjectModalOpen((prev) => !prev);
                  }}
                  className="tw-relative tw-cursor-pointer tw-flex tw-gap-2 tw-bg-[#F1F2F3] tw-p-4 tw-rounded-xl tw-max-w-[35rem]"
                >
                  <div className="tw-relative tw-rounded-xl tw-w-[7.5rem] tw-h-[4.5rem]">
                    <Image
                      src={selectedProject?.image ?? tempProjectImg}
                      alt={selectedProject?.name ?? "project"}
                      onError={() =>
                        setSelectedImage((prev) => ({
                          ...prev,
                          image: tempProjectImg,
                        }))
                      }
                      className="!tw-rounded-xl"
                      fill
                      placeholder="blur"
                      blurDataURL={blurDataURL(300, 200)}
                    />
                  </div>
                  <div>
                    <p className="tw-text-primary-black tw-my-1">
                      {+selectedProject?.subProjectsCount ?? 0} Sub-projects
                    </p>
                    <p className="tw-text-2xl tw-font-bold tw-my-1">
                      {selectedProject?.name ?? ""}
                    </p>
                  </div>
                  {isRemoveAble && (
                    <div className="tw-absolute tw-top-2 tw-right-4">
                      <button
                        onClick={() => {
                          setSelectedProject(null);
                        }}
                        className="tw-text-sm tw-text-[#EF3B41]"
                      >
                        Remove
                      </button>
                    </div>
                  )}
                </div>
              )}
              {/* Visibility */}
              {/* {showPostVisibility && (
                <div className="tw-flex tw-gap-5 tw-items-center">
                  <p className="tw-my-5 tw-text-lg tw-font-medium tw-text-primary-black ">
                    Visibility*
                  </p>
                  <div className="tw-flex tw-gap-2 tw-items-center tw-text-lg">
                    <input
                      type="radio"
                      id="public"
                      name="fav_language"
                      value="Public"
                      checked={selectedValue === "Public"}
                      onChange={handleChange}
                    />
                    <label htmlFor="public">Public</label>
                    <input
                      type="radio"
                      id="private"
                      name="fav_language"
                      value="Private"
                      checked={selectedValue === "Private"}
                      onChange={handleChange}
                    />
                    <label htmlFor="private">Private</label>
                  </div>
                </div>
              )} */}
              {/* Buttons */}
              <div className="tw-absolute  tw-top-1 lg:-tw-top-2 tw-right-0 tw-flex tw-gap-5 tw-items-center">
                {showSaveAsDraft && (
                  <button
                    type="button"
                    disabled={isLoading}
                    onClick={() => {
                      formSubmitHandler(false);
                    }}
                    className={`tw-text-primary-black tw-font-semibold ${
                      isLoading && "tw-cursor-not-allowed"
                    }`}
                  >
                    Save as Draft
                  </button>
                )}
                <CustomButton
                  className={"!tw-px-9 !tw-py-[14px]"}
                  type="button"
                  loading={isLoading}
                  count={8}
                  onClick={() => {
                    // if (!selectedImage) {
                    //   safeToast.error("Cover Photo is Required");
                    //   return;
                    // }
                    formSubmitHandler();
                  }}
                >
                  <span className={"!tw-text-base"}>Publish</span>
                </CustomButton>
              </div>
            </div>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default PostModal;
