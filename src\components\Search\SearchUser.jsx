"use client";
import {
  followUser,
  getProject,
  getSubProjectsFromProjectIds,
  getTeamUsers,
  unFollowUser,
} from "@/app/action";
import Empty from "@/components/Common/Empty";
import useApiRequest from "@/components/helper/hook/useApiRequest";
import SelectProjectSkeleton from "@/components/Loader/SelectProjectSkeleton";
import authStorage from "@/utils/API/AuthStorage";
import { blurDataURL, RESPONSE_STATUS } from "@/utils/function";
import { LeftArrowBackIcon, NoMemberFound } from "@/utils/icons";
import { SearchIcon } from "lucide-react";
import Image from "next/image";
import { useCallback, useContext, useEffect, useRef, useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";
import { valContext } from "../context/ValContext";
import UserCard from "./UserCard";
import toast from "react-hot-toast";
import { useRouter } from "next/navigation";
import { safeToast } from "@/utils/safeToast";

import { useInView } from "react-intersection-observer";

const SearchUser = ({}) => {
  // const [dataList, setDataList] = useState([]);
  // const [pagination, setPagination] = useState({
  //   page: 1,
  //   limit: 20,
  //   total: 0,
  // });
  const [search, setSearch] = useState(null);

  const { ref, inView, entry } = useInView({
    /* Optional options */
    threshold: 0.5,
  });

  // console.log(ref, inView, entry);

  const api = useApiRequest();

  const {
    globalSearch,
    setTotalSearchRecords,
    searchData: dataList,
    setSearchData: setDataList,
    searchPagination: pagination,
    setSearchPagination: setPagination,
  } = useContext(valContext);
  const router = useRouter();

  const resetData = () => {
    setDataList([]);
    setPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
  };

  const followAndUnfollowHandler = async (userId, isFollow) => {
    // console.log(userId, isFollow);
    let apiCall;
    try {
      if (isFollow) {
        apiCall = followUser;
      } else {
        apiCall = unFollowUser;
      }
      const response = await apiCall(userId);
      // console.log(response);
      if (response?.status === RESPONSE_STATUS.SUCCESS) {
        safeToast.success(response?.message);
      } else {
        throw response;
      }
    } catch (error) {
      console.log(error);
      safeToast.error(error?.message);
    }
  };

  // Fetch Projects
  const fetchTeamMember = useCallback(
    async (searchQuery) => {
      let queryParams = {
        page: pagination.page,
        limit: pagination.limit,
        searchQuery: searchQuery?.trim(),
      };
      if (searchQuery) {
        queryParams = {
          ...queryParams,
          searchQuery: searchQuery?.trim(),
        };
      }

      api.sendRequest(
        getTeamUsers,
        (res) => {
          if (searchQuery) {
            setTotalSearchRecords(res?.totalRecords);
          }
          if (pagination.page === 1) {
            setDataList(res?.data);
          } else {
            setDataList((prev) => [...prev, ...res?.data]);
          }
          setPagination((prev) => ({
            ...prev,
            total: res?.totalRecords,
          }));
        },

        queryParams
      );
    },
    [pagination.page, search]
  );

  useEffect(() => {
    if (globalSearch) {
      fetchTeamMember(globalSearch);
    } else {
      fetchTeamMember();
    }
  }, [pagination.page, globalSearch]);

  useEffect(() => {
    if (
      inView &&
      Math.ceil(pagination.total / pagination.limit) > pagination.page
    ) {
      // setPagination((prev) => prev + 1);
      setPagination((prev) => ({
        ...prev,
        page: prev?.page + 1,
      }));
    }
  }, [inView]);

  // useEffect(() => {
  //   if (globalSearch !== search) {
  //     setSearch(globalSearch);
  //     resetData();
  //   }
  // }, [globalSearch]);

  useEffect(() => {
    resetData();
  }, []);

  return (
    <>
      <div className="tw-mb-12 lg:tw-my-2 ">
        {dataList.map((ele) => (
          <div
            className="tw-my-3 tw-cursor-pointer tw-w-full tw-flex tw-justify-between tw-items-center"
            key={ele.id}
            onClick={(e) => {
              e.stopPropagation();

              router.push(`/user/${ele?.slug}`);
            }}
          >
            <UserCard
              ele={ele}
              followAndUnfollowHandler={followAndUnfollowHandler}
            />
          </div>
        ))}
        <div ref={ref}>
          <SelectProjectSkeleton
            className={"!tw-py-6"}
            count={
              pagination?.page === 1 && api.isLoading
                ? 10
                : Math.ceil(pagination?.total / pagination?.limit) >
                    pagination?.page && api.isLoading
                ? 3
                : 0
            }
          />
        </div>
        {/* {api.isLoading && (
          <div ref={ref}>
            <SelectProjectSkeleton className={"!tw-py-6"} count={10} />
          </div>
        )} */}
        {/* <div
          className="tw-h-[80vh] lg:tw-h-screen tw-overflow-auto"
          id="scrollableDiv"
        >
          {dataList?.length > 0 && (
            <InfiniteScroll
              // height={1000}
              dataLength={dataList?.length ?? 0}
              // show-scrollbar css class to show the scroll bar
              className="infinite-scrollbar tw-px-1"
              hasMore={
                Math.ceil(pagination.total / pagination.limit) > pagination.page
              }
              next={() =>
                setPagination((prev) => ({
                  ...prev,
                  page: prev.page + 1,
                }))
              }
              loader={
                <SelectProjectSkeleton className={"!tw-py-6"} count={3} />
              }
              scrollableTarget="scrollableDiv"
            >
              {dataList.map((ele) => (
                <div
                  className="tw-my-3 tw-cursor-pointer tw-w-full tw-flex tw-justify-between tw-items-center"
                  key={ele.id}
                  onClick={(e) => {
                    e.stopPropagation();
                    progress.start();
                    router.push(`/user/${ele?.slug}`);
                  }}
                >
                  <UserCard
                    ele={ele}
                    followAndUnfollowHandler={followAndUnfollowHandler}
                  />
                </div>
              ))}
            </InfiniteScroll>
          )}
        </div> */}
        {!api.isLoading && dataList?.length === 0 && (
          <div className="tw-flex tw-justify-center tw-items-center tw-h-[70vh]">
            <Empty
              icon={<NoMemberFound size={50} />}
              label={"Search Result Not Found!"}
            />
          </div>
        )}
      </div>
    </>
  );
};

export default SearchUser;
