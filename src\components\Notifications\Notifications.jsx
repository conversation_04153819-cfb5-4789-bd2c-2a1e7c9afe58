"use client";
import Image from "next/image";
import { CustomContainer } from "../Common/Custom-Display";
import CustomTitle from "../Common/CustomTitle";
import {
  blurDataURL,
  convertUTCtoLocal,
  parseNotification,
  RESPONSE_STATUS,
  routes,
} from "@/utils/function";
import NotificationSkeleton from "../Loader/NotificationSkeleton";
import { useContext, useEffect, useState } from "react";
import useApiRequest from "../helper/hook/useApiRequest";
import {
  followUser,
  getNotification,
  getOnePost,
  unFollowUser,
  updateMemberCommunityPostStatus,
  updateMemberStatus,
} from "@/app/action";
import InfiniteScroll from "../Common/InfiniteScroll";
import Empty from "../Common/Empty";
import { useRouter } from "next/navigation";
import { valContext } from "../context/ValContext";
import PostPreview from "../HomePage/PostPreview";
import toast from "react-hot-toast";
import { safeToast } from "@/utils/safeToast";
import { LeftArrowBackIcon } from "@/utils/icons";

import UserAvatar from "../Common/UserAvatar";
import authStorage from "@/utils/API/AuthStorage";

const Notifications = () => {
  const [dataList, setDataList] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  });
  const [postData, setPostData] = useState(null);
  const [previewModal, setPreviewModal] = useState(false);
  const api = useApiRequest();
  const singleOneAPI = useApiRequest(false);
  const notificationApi = useApiRequest(false);
  const [postState, setPostState] = useState({
    isFollowed: 0,
  });
  const [userData, setUserData] = useState(null);
  const router = useRouter();

  const { setIsNewNotifications } = useContext(valContext);

  // Get One Post Details
  const getPostDetails = (id) => {
    singleOneAPI.sendRequest(
      getOnePost,
      (res) => {
        setPostData(res?.data);
        setPostState({
          isFollowed: isNaN(+res?.data?.User?.isFollowed)
            ? 0
            : +res?.data?.User?.isFollowed,
        });
      },
      {
        id,
      }
    );
  };
  // Follow and Unfollow handler
  const followAndUnfollowHandler = async (userId, isFollow = true) => {
    let apiCall;
    try {
      if (isFollow) {
        apiCall = followUser;
      } else {
        apiCall = unFollowUser;
      }
      const response = await apiCall(userId);
      if (response?.status === RESPONSE_STATUS.SUCCESS) {
        safeToast.success(response?.message);
      } else {
        throw response;
      }
    } catch (error) {
      console.log(error);
      safeToast.error(error?.message);
    }
  };

  const postPreviewHandler = (ele) => {
    const postId = JSON.parse(ele?.clickAction)?.PostId;
    setPreviewModal(true);
    getPostDetails(postId);
  };

  const userNavigationHandler = (ele) => {
    router.push(
      routes.USER.replace(":slug", JSON.parse(ele?.clickAction)?.userSlug)
    );
  };

  // Update Status
  const updateTeamMemberStatusHandler = (payload, ele = null) => {
    // console.log(payload?.status ? "Yest" : "Nod", payload);
    ele["actionStatus"] = payload?.UserId
      ? payload?.status
      : payload?.isApproved;
    notificationApi.sendRequest(
      payload?.UserId ? updateMemberStatus : updateMemberCommunityPostStatus,
      (res) => {
        // console.log(ele);
        // setDataList((prev) =>
        //   prev?.filter((ele) => ele?.id !== payload?.NotificationId)
        // );

        if (ele) {
          if (payload?.isApproved) {
            if (JSON.parse(ele?.clickAction)?.communitySlug) {
              router.push(
                `/communities/${JSON.parse(ele?.clickAction)?.communitySlug}`
              );
            }
          } else if (payload?.status) {
            if (JSON.parse(ele?.clickAction)?.projectSlug) {
              const baseRoute = JSON.parse(ele?.clickAction)?.ParentId
                ? "/sub-project"
                : "/projects";

              router.push(
                `${baseRoute}/${JSON.parse(ele?.clickAction)?.projectSlug}`
              );
            }
          }
        }
      },
      payload
    );
  };

  useEffect(() => {
    setIsNewNotifications(false);
    const queryParams = {
      page: pagination.page,
      limit: pagination.limit,
    };
    api.sendRequest(
      getNotification,
      (res) => {
        if (pagination.page === 1) {
          setDataList(res?.data?.data);
        } else {
          setDataList((prev) => [...prev, ...res?.data?.data]);
        }
        setPagination((prev) => ({
          ...prev,
          total: res?.data?.totalRecords,
        }));
      },

      queryParams
    );
  }, [pagination.page]);

  useEffect(() => {
    const user = authStorage.getProfileDetails();
    setUserData(user);
  }, []);
  return (
    <>
      <PostPreview
        isCurrentUser={true}
        isOpen={previewModal}
        setIsOpen={setPreviewModal}
        data={postData}
        setData={setPostData}
        isLoading={singleOneAPI.isLoading}
        followAndUnfollowHandler={(userId, isFollow) => {
          followAndUnfollowHandler(userId, isFollow);
        }}
        postState={postState}
        setPostState={setPostState}
        isDraft
      />
      <CustomContainer className="tw-py-4">
        <div className="tw-flex tw-flex-row  tw-justify-center tw-items-center">
          <button
            type="button"
            onClick={() => {
              // progress.start(0, 1);

              router.back();
            }}
            className=""
          >
            <LeftArrowBackIcon />
          </button>
          <CustomTitle
            className="tw-w-full !tw-justify-center"
            name="Notifications"
          />
        </div>
        {!api.isLoading && dataList?.length === 0 && (
          <div className="tw-flex tw-justify-center tw-items-center tw-h-[50dvh]">
            <Empty
              className="tw-text-2xl tw-font-semibold "
              label={"You're all caught up!"}
              subLabel={"You have no Notifications at the moment."}
              iconRequired={false}
            />
          </div>
        )}
        <div className="tw-mt-5 tw-mb-14 lg:tw-mb-0">
          {/* <NotificationSkeleton /> */}
          {dataList?.map((ele) => {
            const userData = JSON.parse(ele?.clickAction) ?? null;
            const data = parseNotification(
              ele?.body,
              ele?.actionStatus,
              router,
              progress,
              ele,
              userData
            );
            let payload = {
              NotificationId: ele?.id,
            };

            if (JSON.parse(ele?.clickAction)?.ProjectId) {
              payload = {
                ...payload,
                id: JSON.parse(ele?.clickAction)?.ProjectId,
                access: JSON.parse(ele?.clickAction)?.access,
                UserId: JSON.parse(ele?.clickAction)?.UserId,
              };
            }
            if (JSON.parse(ele?.clickAction)?.PostId) {
              payload = {
                ...payload,
                id: JSON.parse(ele?.clickAction)?.PostId,
              };
            }
            return (
              <div
                key={ele?.id}
                onClick={() => {
                  if (data?.type === "post_request") {
                    postPreviewHandler(ele);
                    return;
                  }
                  if (
                    data?.type !== "project" &&
                    data?.type !== "follow" &&
                    data?.type !== "subProjectInvitation" &&
                    data?.type !== "subProjectInvitationStatus" &&
                    data?.type !== "invitationAccept" &&
                    data?.type !== "invitationStatus"
                  ) {
                  }
                  if (data?.type === "follow") {
                    userNavigationHandler(ele);
                  } else if (
                    data?.type === "postComment" ||
                    data?.type === "postMention" ||
                    data?.type === "projectMention" ||
                    data?.type === "likePost"
                  ) {
                    router.push(
                      routes.SINGLE_POST.replace(
                        ":slug",
                        JSON.parse(ele?.clickAction)?.postSlug
                      )
                    );
                  } else if (
                    data?.type === "joinCommunity" ||
                    data?.type === "post_requestStatus"
                  ) {
                    router.push(
                      routes.SINGLE_COMMUNITIES.replace(
                        ":slug",
                        JSON.parse(ele?.clickAction)?.communitySlug
                      )
                    );
                  } else if (data?.type === "todoDue") {
                    router.push(routes.TO_DOS);
                  } else if (data?.type === "project") {
                    if (JSON.parse(ele?.clickAction)?.isPrivateProject) {
                      safeToast.error(
                        "This Project is Private. Please accept the invitation to view this project"
                      );
                      return;
                    }

                    router.push(
                      routes.SINGLE_PROJECTS.replace(
                        ":slug",
                        JSON.parse(ele?.clickAction)?.projectSlug
                      )
                    );
                  } else if (data?.type === "subProjectInvitation") {
                    if (JSON.parse(ele?.clickAction)?.isPrivateSubProject) {
                      safeToast.error(
                        "This Project is Private. Please accept the invitation to view this project"
                      );
                      return;
                    }

                    router.push(
                      routes.SINGLE_SUB_PROJECT.replace(
                        ":slug",
                        JSON.parse(ele?.clickAction)?.projectSlug
                      )
                    );
                  } else if (
                    data?.type === "invitationStatus" ||
                    data?.type === "invitationAccept"
                  ) {
                    if (
                      JSON.parse(ele?.clickAction)?.isPrivateProject &&
                      !ele?.actionStatus
                    ) {
                      safeToast.error(
                        "This Project is Private. Please accept the invitation to view this project"
                      );
                      return;
                    }

                    router.push(
                      routes.SINGLE_PROJECTS.replace(
                        ":slug",
                        JSON.parse(ele?.clickAction)?.projectSlug
                      )
                    );
                  } else if (data?.type === "subProjectInvitationStatus") {
                    if (
                      JSON.parse(ele?.clickAction)?.isPrivateSubProject &&
                      !ele?.actionStatus
                    ) {
                      safeToast.error(
                        "This Project is Private. Please accept the invitation to view this project"
                      );
                      return;
                    }

                    router.push(
                      routes.SINGLE_SUB_PROJECT.replace(
                        ":slug",
                        JSON.parse(ele?.clickAction)?.projectSlug
                      )
                    );
                  }

                  // router.push(
                  //   `/user/${JSON.parse(ele?.clickAction)?.userSlug}`
                  // );
                }}
                className="tw-bg-[#F5F7F8] tw-rounded-3xl tw-cursor-pointer tw-mb-5 tw-py-4 tw-px-4 md:tw-px-6 md:tw-py-5 tw-flex tw-flex-col md:tw-flex-row tw-justify-between tw-gap-4 md:tw-items-center"
              >
                {/* Avatar + Content Inline */}
                <div className="tw-flex tw-gap-3 tw-items-center">
                  <div
                    onClick={(e) => {
                      e.stopPropagation();
                      userNavigationHandler(ele);
                    }}
                  >
                    <UserAvatar
                      imageUrl={userData?.image}
                      userName={data?.userName ?? data?.name}
                      imageParentClassName="!tw-w-14 !tw-h-14 md:!tw-w-16 md:!tw-h-16 tw-flex-shrink-0"
                      userNameClassName="!tw-text-3xl"
                    />
                  </div>
                  <div>
                    <p className="tw-text-base md:tw-text-lg tw-text-primary-black">
                      <span
                        onClick={(e) => {
                          e.stopPropagation();
                          userNavigationHandler(ele);
                        }}
                        className="tw-font-semibold"
                      >
                        {data?.name}
                      </span>
                      {data?.middleLabel}
                      <span
                        className={`${
                          JSON.parse(ele?.clickAction)?.projectSlug &&
                          "tw-cursor-pointer"
                        }`}
                      >
                        {data?.community}.
                      </span>
                    </p>
                    <p className="tw-text-secondary-text tw-text-xs md:tw-text-sm">
                      {convertUTCtoLocal(ele?.createdAt, "MMM DD, YYYY")}
                    </p>
                  </div>
                </div>

                {/* Accept / Reject / Preview Buttons */}
                {(ele?.type === "team-invitation" ||
                  ele?.type === "post-created") &&
                  ele?.actionStatus === null && (
                    <div className="tw-flex tw-flex-row tw-justify-center lg:tw-justify-start tw-gap-4 tw-items-center">
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation();
                          if (JSON.parse(ele?.clickAction)?.ProjectId) {
                            payload = {
                              ...payload,
                              status: true,
                            };
                          }
                          if (JSON.parse(ele?.clickAction)?.PostId) {
                            payload = {
                              ...payload,
                              isApproved: true,
                            };
                          }
                          updateTeamMemberStatusHandler(payload, ele);
                        }}
                        className="tw-bg-[#10BE5B] tw-text-white tw-font-semibold tw-px-4 tw-py-2 tw-rounded-full tw-text-sm"
                      >
                        Accept
                      </button>

                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation();
                          if (JSON.parse(ele?.clickAction)?.ProjectId) {
                            payload = {
                              ...payload,
                              status: false,
                            };
                          }
                          if (JSON.parse(ele?.clickAction)?.PostId) {
                            payload = {
                              ...payload,
                              isApproved: false,
                            };
                          }
                          updateTeamMemberStatusHandler(payload, ele);
                        }}
                        className="tw-text-[#EF3B41] tw-font-semibold tw-text-sm"
                      >
                        Reject
                      </button>

                      {ele?.type === "post-created" && (
                        <button
                          type="button"
                          onClick={(e) => {
                            e.stopPropagation();
                            postPreviewHandler(ele);
                          }}
                          className="tw-text-primary-purple tw-font-semibold tw-text-sm"
                        >
                          Preview
                        </button>
                      )}
                    </div>
                  )}
              </div>
            );
          })}
          <InfiniteScroll
            threshold={90}
            loadMoreFunction={() => {
              if (
                pagination.page < Math.ceil(pagination.total / pagination.limit)
              ) {
                setPagination((prev) => ({
                  ...prev,
                  page: prev?.page + 1,
                }));
              }
            }}
            isLoading={api.isLoading}
            loadingComponent={<NotificationSkeleton />}
            timeout={10}
            // loadOff={loadOff}
          />
        </div>
      </CustomContainer>
    </>
  );
};

export default Notifications;
