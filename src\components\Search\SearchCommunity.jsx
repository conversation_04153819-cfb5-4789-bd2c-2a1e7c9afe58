"use client";
import { useCallback, useContext, useEffect, useState } from "react";
import useApiRequest from "../helper/hook/useApiRequest";
import { NoCommunityData, NoMemberFound, UserGroupIcon } from "@/utils/icons";
import { useRouter } from "next/navigation";
import { getCommunity, joinCommunity } from "@/app/action";
import toast from "react-hot-toast";
import CommunityCard from "../Communities/CommunityCard";
import InfiniteScroll from "../Common/InfiniteScroll";
import CommunitySkeleton from "../Loader/CommunitySkeleton";
import { valContext } from "../context/ValContext";
import Empty from "../Common/Empty";
import { safeToast } from "@/utils/safeToast";

const SearchCommunity = ({}) => {
  // const [dataList, setDataList] = useState([]);
  // const [pagination, setPagination] = useState({
  //   page: 1,
  //   limit: 20,
  //   total: 0,
  // });

  const {
    globalSearch,
    setTotalSearchRecords,
    searchData: dataList,
    setSearchData: setDataList,
    searchPagination: pagination,
    setSearchPagination: setPagination,
  } = useContext(valContext);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const api = useApiRequest();

  // Reset State
  const resetState = () => {
    setDataList([]);
    setPagination({
      page: 1,
      limit: 18,
      total: 0,
    });
  };

  // Tabs Array
  const communitiesCategory = [
    {
      label: "Created",
      value: "created",
    },
    {
      label: "Joined",
      value: "joined",
    },

    {
      label: "Explore",
      value: "explore",
    },
  ];

  const fetchProjects = useCallback(
    async (searchQuery) => {
      setIsLoading(true); // ✅ Set loading before API call

      let queryParams = {
        page: pagination.page,
        limit: pagination.limit,
      };
      if (searchQuery) {
        // resetState();
        queryParams = {
          ...queryParams,
          searchQuery: searchQuery?.trim(),
        };
      }
      try {
        await api.sendRequest(
          getCommunity,
          (res) => {
            if (searchQuery) {
              setTotalSearchRecords(res?.data?.totalRecords);
            }
            if (pagination.page === 1) {
              setDataList(res?.data?.data);
            } else {
              setDataList((prev) => [...prev, ...res?.data?.data]);
            }
            setPagination((prev) => ({
              ...prev,
              total: res?.data?.totalRecords,
            }));
          },
          queryParams
        );
      } catch (error) {
        console.error("Error fetching projects:", error);
      } finally {
        setIsLoading(false); // ✅ Set loading false after API response (success or error)
      }
    },
    [pagination.page, globalSearch]
  );

  useEffect(() => {
    if (globalSearch) {
      fetchProjects(globalSearch);
    } else {
      fetchProjects();
    }
  }, [pagination.page, globalSearch]);

  useEffect(() => {
    resetState();
  }, []);

  // useEffect(() => {
  //   if (globalSearch !== search) {
  //     setSearch(globalSearch);
  //   } else {
  //     setSearch(null);
  //   }
  //   resetState();
  // }, [globalSearch]);

  return (
    <>
      <div>
        {/* Title , Tabs and Add Project Button */}

        <div className="tw-my-5 !tw-pb-6 tw-grid  lg:tw-grid-cols-3 2xl:tw-grid-cols-4 tw-gap-7  tw-w-full ">
          {/* <CommunitySkeleton /> */}
          {dataList?.map((ele) => (
            <div
              key={ele?.id}
              onClick={(e) => {
                e.stopPropagation();
                router.push(`/communities/${ele?.slug}`);
              }}
              // className="tw-bg-[#F5F7F8] tw-cursor-pointer tw-rounded-3xl tw-px-5 tw-py-7 tw-flex tw-flex-col tw-items-center tw-gap-4"
              className="lg:tw-bg-[#F5F7F8] tw-cursor-pointer tw-rounded-3xl lg:tw-px-5 lg:tw-py-7 tw-flex lg:tw-flex-col tw-items-center tw-gap-4"
            >
              <CommunityCard
                ele={ele}
                isCreatedByMe={false}
                joinHandler={async (id) => {
                  api.sendRequest(
                    joinCommunity,
                    (res) => {
                      //   console.log(res);
                      safeToast.success(res?.message);
                    },
                    {
                      id,
                    }
                  );
                }}
              />
            </div>
          ))}
          <InfiniteScroll
            threshold={90}
            loadMoreFunction={() => {
              if (
                Math.ceil(pagination?.total / pagination?.limit) >
                pagination?.page
              ) {
                setPagination((prev) => ({
                  ...prev,
                  page: prev?.page + 1,
                }));
              }
            }}
            isLoading={isLoading}
            loadingComponent={<CommunitySkeleton />}
            timeout={10}
            // loadOff={loadOff}
          />
        </div>
        {!api.isLoading && dataList?.length === 0 && (
          <div className="tw-flex tw-justify-center tw-items-center tw-h-[70vh]">
            <Empty
              icon={<NoMemberFound size={50} />}
              label={"Search Result Not Found!"}
            />
          </div>
        )}
      </div>
    </>
  );
};

export default SearchCommunity;
